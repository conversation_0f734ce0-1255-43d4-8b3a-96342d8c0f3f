# DMD模型编码器特征可视化

本项目提供了对DMD模型中公共编码器和私有编码器特征的可视化功能。

## 功能概述

DMD模型包含两种类型的编码器：
1. **公共编码器 (Common Encoder)**: 提取跨模态的共享特征
2. **私有编码器 (Private Encoder)**: 提取每个模态特有的特征

## 文件说明

### 1. `visualize_encoder_features.py`
专门用于可视化DMD模型编码器特征的脚本。

**主要功能：**
- 提取公共编码器特征 (`c_l`, `c_v`, `c_a`)
- 提取私有编码器特征 (`s_l`, `s_v`, `s_a`)
- 使用t-SNE进行降维可视化
- 根据模态和情感标签进行颜色/形状编码

**使用方法：**
```bash
# 可视化公共编码器特征
python visualize_encoder_features.py --encoder_type common

# 可视化私有编码器特征
python visualize_encoder_features.py --encoder_type private

# 同时可视化两种编码器特征
python visualize_encoder_features.py --encoder_type both
```

### 2. `visualize_raw_dataset_features.py` (已更新)
原有的可视化脚本，现在增加了编码器特征可视化选项。

**新增功能：**
- `--visualization_type common`: 可视化公共编码器特征
- `--visualization_type private`: 可视化私有编码器特征
- `--visualization_type all`: 可视化所有类型的特征

## 参数说明

### 通用参数
- `--dataset_name`: 数据集名称 (默认: 'mosi')
- `--num_batches`: 处理的批次数量 (默认: 3)
- `--max_samples`: t-SNE使用的最大样本数 (默认: 300)
- `--perplexity`: t-SNE的perplexity参数 (默认: 15)
- `--n_iter`: t-SNE的迭代次数 (默认: 500)
- `--model_path`: 训练好的模型路径 (默认: './pt/dmd.pth')

### 输出文件
- `--save_path_common`: 公共编码器特征可视化保存路径
- `--save_path_private`: 私有编码器特征可视化保存路径

## 可视化说明

### 图例说明
- **形状**: 表示模态类型
  - ○ (圆形): 文本模态
  - ■ (方形): 音频模态
  - ▲ (三角形): 视频模态

- **颜色**: 表示情感标签
  - 红色: 正面情感 (Positive)
  - 灰色: 中性情感 (Neutral)
  - 蓝色: 负面情感 (Negative)

### 特征解释

**公共编码器特征 (Common Encoder Features)**:
- 表示不同模态之间的共享信息
- 理想情况下，相同情感的不同模态特征应该聚集在一起
- 体现了模型学习到的跨模态共同表示

**私有编码器特征 (Private Encoder Features)**:
- 表示每个模态特有的信息
- 不同模态的特征应该相对分离
- 体现了模型学习到的模态特定表示

## 使用示例

### 示例1: 快速可视化公共编码器特征
```bash
python visualize_encoder_features.py \
    --encoder_type common \
    --dataset_name mosi \
    --num_batches 5 \
    --max_samples 500
```

### 示例2: 可视化所有类型的特征
```bash
python visualize_raw_dataset_features.py \
    --visualization_type all \
    --dataset_name mosi \
    --num_batches 3 \
    --max_samples 300
```

### 示例3: 自定义输出路径
```bash
python visualize_encoder_features.py \
    --encoder_type both \
    --save_path_common ./results/common_features.png \
    --save_path_private ./results/private_features.png
```

## 输出文件

运行脚本后会生成以下文件：
- `common_encoder_features_sentiment_tsne.png`: 公共编码器特征可视化
- `private_encoder_features_sentiment_tsne.png`: 私有编码器特征可视化

每个图像包含：
- t-SNE降维后的2D散点图
- 图例说明模态和情感的对应关系
- 统计信息（样本数量、情感分布等）

## 注意事项

1. **模型路径**: 确保 `--model_path` 指向正确的训练好的DMD模型文件
2. **数据路径**: 确保数据集路径在配置文件中正确设置
3. **WSL环境**: 脚本已适配WSL环境的路径转换
4. **内存使用**: 大量样本可能消耗较多内存，可通过 `--max_samples` 控制
5. **计算时间**: t-SNE计算可能需要一些时间，可通过减少 `--n_iter` 加速

## 故障排除

如果遇到问题：
1. 检查模型文件是否存在且可访问
2. 确认数据集路径配置正确
3. 检查Python环境中是否安装了所需依赖
4. 查看日志输出获取详细错误信息
