# 增强特征可视化指南

本指南介绍如何使用增强的特征可视化功能，支持原始数据特征和模型融合特征的可视化。

## 功能概述

### 1. 原始数据特征可视化
- **功能**：可视化数据集中的原始特征（未经模型处理）
- **特点**：
  - 三种形状表示不同模态：○文本、■音频、▲视频
  - 三种颜色表示不同情感：红色(积极)、深灰色(中立)、蓝色(消极)
  - 使用t-SNE降维到2D空间进行可视化

### 2. 模型融合特征可视化
- **功能**：可视化DMD模型最终融合后的特征
- **特点**：
  - 显示模型学习到的高级语义表示
  - 三种颜色表示不同情感：红色(积极)、深灰色(中立)、蓝色(消极)
  - 展示模型如何将多模态信息融合

## 使用方法

### 基本用法

```bash
# 只可视化原始特征
python visualize_raw_dataset_features.py --visualization_type raw

# 只可视化融合特征
python visualize_raw_dataset_features.py --visualization_type fusion

# 同时可视化两种特征
python visualize_raw_dataset_features.py --visualization_type both
```

### 完整参数

```bash
python visualize_raw_dataset_features.py \
    --dataset_name mosi \
    --num_batches 5 \
    --max_samples 500 \
    --perplexity 20 \
    --n_iter 500 \
    --visualization_type both \
    --model_path ./pt/dmd.pth \
    --save_path_raw raw_features_sentiment.png \
    --save_path_fusion fusion_features_sentiment.png
```

### 参数说明

- `--dataset_name`: 数据集名称 (默认: mosi)
- `--num_batches`: 处理的批次数量 (默认: 3)
- `--max_samples`: t-SNE的最大样本数 (默认: 300)
- `--perplexity`: t-SNE的perplexity参数 (默认: 15)
- `--n_iter`: t-SNE的迭代次数 (默认: 500)
- `--visualization_type`: 可视化类型 (raw/fusion/both)
- `--model_path`: 训练好的模型路径 (默认: ./pt/dmd.pth)
- `--save_path_raw`: 原始特征图像保存路径
- `--save_path_fusion`: 融合特征图像保存路径

### 使用便捷脚本

```bash
# 交互式运行
python run_enhanced_visualization.py
```

## 输出文件

### 原始特征可视化
- 文件名：`raw_dataset_features_sentiment_tsne.png`
- 内容：显示三种模态的原始特征分布
- 图例：形状表示模态，颜色表示情感

### 融合特征可视化
- 文件名：`model_fusion_features_sentiment_tsne.png`
- 内容：显示模型融合后的特征分布
- 图例：颜色表示情感

## 技术细节

### 情感标签处理
1. 优先使用pkl文件中的`annotations`字段
2. 如果没有annotations，从`regression_labels`转换：
   - `regression_labels < 0` → Negative
   - `regression_labels > 0` → Positive
   - `regression_labels = 0` → Neutral

### 模型融合特征提取
- 从DMD模型的最终层提取融合特征
- 包含文本、音频、视频三个模态的交叉注意力特征
- 特征维度：300 (每个模态100维)

### 可视化配色方案
- **红色** (#FF6B6B)：积极情感
- **深灰色** (#555555)：中立情感
- **蓝色** (#45B7D1)：消极情感

## 示例输出

### 原始特征可视化
```
✓ Raw dataset feature visualization completed!
✓ Output saved to: raw_dataset_features_sentiment_tsne.png
✓ Shapes: ○Text, ■Audio, ▲Video
✓ Colors: Red=Positive, Gray=Neutral, Blue=Negative
```

### 融合特征可视化
```
✓ Model fusion feature visualization completed!
✓ Output saved to: model_fusion_features_sentiment_tsne.png
✓ Colors: Red=Positive, Gray=Neutral, Blue=Negative
```

## 注意事项

1. **模型路径**：确保模型文件存在于指定路径
2. **内存使用**：大样本数量可能需要更多内存
3. **计算时间**：t-SNE计算可能需要几分钟时间
4. **GPU支持**：模型推理会自动使用可用的GPU

## 故障排除

### 常见问题
1. **模型文件未找到**：检查`--model_path`参数
2. **内存不足**：减少`--max_samples`参数
3. **数据文件未找到**：检查数据集路径配置

### 调试模式
```bash
# 使用较少样本进行快速测试
python visualize_raw_dataset_features.py \
    --visualization_type both \
    --num_batches 2 \
    --max_samples 100
```
