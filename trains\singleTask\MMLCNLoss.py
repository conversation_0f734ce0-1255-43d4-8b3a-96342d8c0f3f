import math
from typing import Dict, List, <PERSON><PERSON>

import torch
import torch.nn as nn
import torch.nn.functional as F


class _KMeans:
    """Simple KMeans in torch for small batches.

    Supports dynamic k (<= n) and returns labels and centroids.
    """

    def __init__(self, n_iters: int = 10):
        self.n_iters = n_iters

    @staticmethod
    def _init_centroids(x: torch.Tensor, k: int) -> torch.Tensor:
        # x: [N, D]
        n = x.size(0)
        if n == 0:
            return x.new_zeros((0, x.size(1)))
        if k >= n:
            # use unique points or pad repeats if needed
            if n == 1:
                return x.repeat(k, 1)
            idx = torch.randperm(n, device=x.device)[:k]
            return x[idx]
        idx = torch.randperm(n, device=x.device)[:k]
        return x[idx]

    @staticmethod
    def _pairwise_sqdist(x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
        # x: [N, D], y: [K, D] -> [N, K]
        x2 = (x * x).sum(dim=1, keepdim=True)
        y2 = (y * y).sum(dim=1).unsqueeze(0)
        xy = x @ y.t()
        return torch.clamp(x2 - 2 * xy + y2, min=0.0)

    def __call__(self, x: torch.Tensor, k: int) -> Tuple[torch.Tensor, torch.Tensor]:
        # returns labels [N] and centroids [k, D]
        n = x.size(0)
        if n == 0 or k <= 0:
            return x.new_zeros((n,), dtype=torch.long), x.new_zeros((0, x.size(1)))
        k = min(k, n)
        c = self._init_centroids(x, k)
        labels = torch.zeros(n, dtype=torch.long, device=x.device)
        for _ in range(self.n_iters):
            d = self._pairwise_sqdist(x, c)  # [N, k]
            new_labels = torch.argmin(d, dim=1)
            if torch.equal(labels, new_labels):
                break
            labels = new_labels
            # recompute centroids
            for i in range(k):
                mask = labels == i
                if mask.any():
                    c[i] = x[mask].mean(dim=0)
                else:
                    # reinit empty cluster to random point
                    ridx = torch.randint(0, n, (1,), device=x.device)
                    c[i] = x[ridx]
        return labels, c


def _sq_mahalanobis(x: torch.Tensor, y: torch.Tensor, M: torch.Tensor) -> torch.Tensor:
    """Squared Mahalanobis: (x - y)^T M (x - y)
    x: [N, D] or [D], y: broadcastable to x, M: [D, D]
    returns: [N]
    """
    diff = x - y
    # [N, D] @ [D, D] -> [N, D]
    Md = diff @ M
    return (Md * diff).sum(dim=-1)


class MetricClusterHingeLoss(nn.Module):
    """Implements formula-10 for multi-modal sentiment clustering with local metrics.

    - Builds up to 7 clusters per batch: 3 negative, 1 neutral, 3 positive.
    - Learns one PSD metric per fine-grained cluster via L_i L_i^T.
    - Triplet-like hinge over cluster centers enforces separation with adaptive margins.

    Inputs:
    - feats: Tensor [N, D] features from shared encoder (stacked across modalities)
    - labels: Tensor [N] real-valued sentiment scores in [-3, 3]

    Hyperparams (with defaults if missing in args):
    - beta: 1e-3, v1: 1.0, v2: 1.0, alpha: 0.3, kmeans_iters: 10, 
      max_pairs_per_cluster: 256 (to bound O(N^2) term cost)
    """

    def __init__(self, feature_dim: int = None, beta: float = 1e-3, v1: float = 3.0, v2: float = 3.0,
                 alpha: float = 0.3, kmeans_iters: int = 1000, max_pairs_per_cluster: int = 1024):
        super().__init__()
        self.beta = beta
        self.v1 = v1
        self.v2 = v2
        self.alpha = alpha
        self.kmeans = _KMeans(n_iters=kmeans_iters)
        self.max_pairs = max_pairs_per_cluster
        self.feature_dim = feature_dim

        # We maintain 7 learnable local metrics L_i (lower-triangular via free matrix), M_i = L_i L_i^T
        self.num_clusters = 7
        if feature_dim is not None:
            self._init_metrics(feature_dim)
        else:
            self.L_params = None  # lazy init on first forward

    def _init_metrics(self, d: int, device: torch.device = None):
        Ls = []
        for _ in range(self.num_clusters):
            # small init near identity
            if device is None:
                L = torch.eye(d) * 0.1
                L += 0.01 * torch.randn(d, d)
            else:
                L = torch.eye(d, device=device) * 0.1
                L += 0.01 * torch.randn(d, d, device=device)
            Ls.append(nn.Parameter(L))
        self.L_params = nn.ParameterList(Ls)
        self.feature_dim = d

    def _get_metric(self, idx: int) -> torch.Tensor:
        L = self.L_params[idx]
        M = L @ L.t()
        # add tiny jitter for stability
        return M

    @staticmethod
    def _cluster_ranges() -> Dict[str, Tuple[float, float]]:
        # fine-grained ranges for mapping clusters
        return {
            'wn': (-1.5, -0.5),
            'n': (-0.5, 0.5),
            'wp': (0.5, 1.5),
            'np': (1.5, 2.5),
            'hp': (2.5, 3.1),  # upper bound inclusive
            'nn': (-2.5, -1.5),
            'hn': (-3.1, -2.5),
        }

    @staticmethod
    def _cluster_index_map() -> Dict[str, int]:
        # fixed ordering of 7 clusters
        # 0: highly negative, 1: negative, 2: weakly negative, 3: neutral,
        # 4: weakly positive, 5: positive, 6: highly positive
        return {'hn': 0, 'nn': 1, 'wn': 2, 'n': 3, 'wp': 4, 'np': 5, 'hp': 6}

    def _assign_fine_label(self, y: torch.Tensor) -> str:
        yv = y.item()
        rng = self._cluster_ranges()
        for k, (lo, hi) in rng.items():
            if lo <= yv <= hi:
                return k
        # fallback by sign
        if yv > 0.5:
            return 'wp'
        if yv < -0.5:
            return 'wn'
        return 'n'

    def _build_clusters(self, feats: torch.Tensor, labels: torch.Tensor) -> Dict[int, Dict]:
        """Build clusters using K-means, then assign cluster identity based on majority label range.

        Returns: dict mapping cluster-id -> {
            'mask': BoolTensor [N],
            'center': Tensor [D],
            'fine': str,
            'similar_mask': BoolTensor [N_cluster],  # 簇内相似实例的mask
            'dissimilar_mask': BoolTensor [N_cluster]  # 簇内不相似实例的mask
        }
        """
        device = feats.device

        # 使用K-means对所有特征进行聚类，最多7个簇
        n_samples = feats.size(0)
        if n_samples == 0:
            return {}

        k = min(7, n_samples)  # 最多7个簇
        cluster_labels, cluster_centers = self.kmeans(feats, k)

        clusters: Dict[int, Dict] = {}

        # 为每个K-means簇分配标识，基于占比最大的标签范围
        label_ranges = self._cluster_ranges()
        key_to_idx = self._cluster_index_map()

        for cluster_id in range(k):
            # 获取属于当前簇的样本
            cluster_mask = cluster_labels == cluster_id
            if not cluster_mask.any():
                continue

            cluster_feats = feats[cluster_mask]
            cluster_sample_labels = labels[cluster_mask]
            cluster_center = cluster_centers[cluster_id]

            # 统计每个标签范围的样本数量
            range_counts = {}
            for range_name, (low, high) in label_ranges.items():
                count = ((cluster_sample_labels >= low) & (cluster_sample_labels <= high)).sum().item()
                range_counts[range_name] = count

            # 找到占比最大的标签范围作为簇标识
            if sum(range_counts.values()) > 0:
                cluster_fine_label = max(range_counts.items(), key=lambda x: x[1])[0]
            else:
                # 如果没有样本落在任何范围内，使用平均值决定
                mean_label = cluster_sample_labels.mean().item()
                if mean_label > 0.5:
                    cluster_fine_label = 'wp'
                elif mean_label < -0.5:
                    cluster_fine_label = 'wn'
                else:
                    cluster_fine_label = 'n'

            # 确定簇内的相似和不相似实例
            cluster_range = label_ranges[cluster_fine_label]
            similar_mask_local = ((cluster_sample_labels >= cluster_range[0]) &
                                (cluster_sample_labels <= cluster_range[1]))
            dissimilar_mask_local = ~similar_mask_local

            # 存储簇信息
            cluster_idx = key_to_idx[cluster_fine_label]
            clusters[cluster_idx] = {
                'mask': cluster_mask,
                'center': cluster_center,
                'fine': cluster_fine_label,
                'similar_mask': similar_mask_local,
                'dissimilar_mask': dissimilar_mask_local,
                'cluster_feats': cluster_feats,
                'cluster_labels': cluster_sample_labels
            }

        return clusters

    def forward(self, feats: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """Compute formula-10 loss on batch features.

        feats: [N, D]
        labels: [N] real-valued
        """
        if self.L_params is None:
            self._init_metrics(feats.size(1), device=feats.device)

        device = feats.device
        d = feats.size(1)
        I = torch.eye(d, device=device)

        clusters = self._build_clusters(feats, labels)
        if len(clusters) == 0:
            return feats.new_tensor(0.0, requires_grad=True)

        # precompute centers list for margins
        centers = {}
        active_ids = sorted(list(clusters.keys()))
        for cid in active_ids:
            centers[cid] = clusters[cid]['center']

        total_sim = feats.new_tensor(0.0)
        total_dis = feats.new_tensor(0.0)
        total_trip = feats.new_tensor(0.0)
        cnt_sim = 0
        cnt_dis = 0
        cnt_trip = 0

        for cid in active_ids:
            info = clusters[cid]
            cluster_center = info['center']
            similar_mask = info['similar_mask']
            dissimilar_mask = info['dissimilar_mask']
            cluster_feats = info['cluster_feats']

            if cluster_feats.size(0) == 0:
                continue

            M = self._get_metric(cid) + self.beta * I

            # 项1: 拉近相似实例 - 相似实例之间的距离最小化
            if similar_mask.any():
                similar_feats = cluster_feats[similar_mask]
                if similar_feats.size(0) >= 2:
                    # 计算相似实例之间的成对距离
                    n_similar = similar_feats.size(0)
                    ii, jj = torch.triu_indices(n_similar, n_similar, offset=1, device=device)
                    if ii.numel() > self.max_pairs:
                        sel = torch.randperm(ii.numel(), device=device)[:self.max_pairs]
                        ii, jj = ii[sel], jj[sel]

                    x1 = similar_feats[ii]
                    x2 = similar_feats[jj]
                    d_similar = _sq_mahalanobis(x1, x2, M)
                    total_sim = total_sim + d_similar.mean()
                    cnt_sim += 1

                # 相似实例到簇中心的距离也要最小化
                center_expanded = cluster_center.unsqueeze(0).expand(similar_feats.size(0), -1)
                d_to_center = _sq_mahalanobis(similar_feats, center_expanded, M)
                total_sim = total_sim + d_to_center.mean()
                cnt_sim += 1

            # 项2: 推远不相似实例 - 不相似实例到簇中心的距离最大化
            if dissimilar_mask.any():
                dissimilar_feats = cluster_feats[dissimilar_mask]
                # 计算不相似实例到簇中心的距离，使用负值来实现最大化
                center_expanded = cluster_center.unsqueeze(0).expand(dissimilar_feats.size(0), -1)
                d_dissimilar_to_center = _sq_mahalanobis(dissimilar_feats, center_expanded, M)
                total_dis = total_dis - d_dissimilar_to_center.mean()  # 负号实现推远
                cnt_dis += 1

        # 项3: 簇间分离 - 确保不同簇的中心距离足够远
        for i in range(len(active_ids)):
            for j in range(i + 1, len(active_ids)):
                cid_i, cid_j = active_ids[i], active_ids[j]
                ci, cj = centers[cid_i], centers[cid_j]

                # 计算簇中心间的距离，使用负值来最大化距离
                center_distance = torch.norm(ci - cj, p=2)
                total_trip = total_trip - center_distance  # 负号实现最大化簇间距离
                cnt_trip += 1

        # 归一化和加权
        sim_term = total_sim / max(cnt_sim, 1)      # 最小化相似实例间距离
        dis_term = total_dis / max(cnt_dis, 1)      # 最大化不相似实例到中心距离（已经是负值）
        trip_term = total_trip / max(cnt_trip, 1)   # 最大化簇间距离（已经是负值）

        # 总损失：拉近相似 + 推远不相似 + 增强簇间分离
        loss = sim_term + self.v1 * dis_term + self.v2 * trip_term
        return loss
