import math
from typing import Dict, List, <PERSON><PERSON>

import torch
import torch.nn as nn
import torch.nn.functional as F


class _KMeans:
    """Simple KMeans in torch for small batches.

    Supports dynamic k (<= n) and returns labels and centroids.
    """

    def __init__(self, n_iters: int = 10):
        self.n_iters = n_iters

    @staticmethod
    def _init_centroids(x: torch.Tensor, k: int) -> torch.Tensor:
        # x: [N, D]
        n = x.size(0)
        if n == 0:
            return x.new_zeros((0, x.size(1)))
        if k >= n:
            # use unique points or pad repeats if needed
            if n == 1:
                return x.repeat(k, 1)
            idx = torch.randperm(n, device=x.device)[:k]
            return x[idx]
        idx = torch.randperm(n, device=x.device)[:k]
        return x[idx]

    @staticmethod
    def _pairwise_sqdist(x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
        # x: [N, D], y: [K, D] -> [N, K]
        x2 = (x * x).sum(dim=1, keepdim=True)
        y2 = (y * y).sum(dim=1).unsqueeze(0)
        xy = x @ y.t()
        return torch.clamp(x2 - 2 * xy + y2, min=0.0)

    def __call__(self, x: torch.Tensor, k: int) -> Tuple[torch.Tensor, torch.Tensor]:
        # returns labels [N] and centroids [k, D]
        n = x.size(0)
        if n == 0 or k <= 0:
            return x.new_zeros((n,), dtype=torch.long), x.new_zeros((0, x.size(1)))
        k = min(k, n)
        c = self._init_centroids(x, k)
        labels = torch.zeros(n, dtype=torch.long, device=x.device)
        for _ in range(self.n_iters):
            d = self._pairwise_sqdist(x, c)  # [N, k]
            new_labels = torch.argmin(d, dim=1)
            if torch.equal(labels, new_labels):
                break
            labels = new_labels
            # recompute centroids
            for i in range(k):
                mask = labels == i
                if mask.any():
                    c[i] = x[mask].mean(dim=0)
                else:
                    # reinit empty cluster to random point
                    ridx = torch.randint(0, n, (1,), device=x.device)
                    c[i] = x[ridx]
        return labels, c


def _sq_mahalanobis(x: torch.Tensor, y: torch.Tensor, M: torch.Tensor) -> torch.Tensor:
    """Squared Mahalanobis: (x - y)^T M (x - y)
    x: [N, D] or [D], y: broadcastable to x, M: [D, D]
    returns: [N]
    """
    diff = x - y
    # [N, D] @ [D, D] -> [N, D]
    Md = diff @ M
    return (Md * diff).sum(dim=-1)


class MetricClusterHingeLoss(nn.Module):
    """Implements formula-10 for multi-modal sentiment clustering with local metrics.

    - Builds up to 7 clusters per batch: 3 negative, 1 neutral, 3 positive.
    - Learns one PSD metric per fine-grained cluster via L_i L_i^T.
    - Triplet-like hinge over cluster centers enforces separation with adaptive margins.

    Inputs:
    - feats: Tensor [N, D] features from shared encoder (stacked across modalities)
    - labels: Tensor [N] real-valued sentiment scores in [-3, 3]

    Hyperparams (with defaults if missing in args):
    - beta: 1e-3, v1: 1.0, v2: 1.0, alpha: 0.3, kmeans_iters: 10, 
      max_pairs_per_cluster: 256 (to bound O(N^2) term cost)
    """

    def __init__(self, feature_dim: int = None, beta: float = 1e-3, v1: float = 10.0, v2: float = 10.0,
                 alpha: float = 0.3, kmeans_iters: int = 10, max_pairs_per_cluster: int = 1024):
        super().__init__()
        self.beta = beta
        self.v1 = v1
        self.v2 = v2
        self.alpha = alpha
        self.kmeans = _KMeans(n_iters=kmeans_iters)
        self.max_pairs = max_pairs_per_cluster
        self.feature_dim = feature_dim

        # We maintain 7 learnable local metrics L_i (lower-triangular via free matrix), M_i = L_i L_i^T
        self.num_clusters = 7
        if feature_dim is not None:
            self._init_metrics(feature_dim)
        else:
            self.L_params = None  # lazy init on first forward

    def _init_metrics(self, d: int, device: torch.device = None):
        Ls = []
        for _ in range(self.num_clusters):
            # small init near identity
            if device is None:
                L = torch.eye(d) * 0.1
                L += 0.01 * torch.randn(d, d)
            else:
                L = torch.eye(d, device=device) * 0.1
                L += 0.01 * torch.randn(d, d, device=device)
            Ls.append(nn.Parameter(L))
        self.L_params = nn.ParameterList(Ls)
        self.feature_dim = d

    def _get_metric(self, idx: int) -> torch.Tensor:
        L = self.L_params[idx]
        M = L @ L.t()
        # add tiny jitter for stability
        return M

    @staticmethod
    def _cluster_ranges() -> Dict[str, Tuple[float, float]]:
        # fine-grained ranges for mapping clusters
        return {
            'wn': (-1.5, -0.5),
            'n': (-0.5, 0.5),
            'wp': (0.5, 1.5),
            'np': (1.5, 2.5),
            'hp': (2.5, 3.1),  # upper bound inclusive
            'nn': (-2.5, -1.5),
            'hn': (-3.1, -2.5),
        }

    @staticmethod
    def _cluster_index_map() -> Dict[str, int]:
        # fixed ordering of 7 clusters
        # 0: highly negative, 1: negative, 2: weakly negative, 3: neutral,
        # 4: weakly positive, 5: positive, 6: highly positive
        return {'hn': 0, 'nn': 1, 'wn': 2, 'n': 3, 'wp': 4, 'np': 5, 'hp': 6}

    def _assign_fine_label(self, y: torch.Tensor) -> str:
        yv = y.item()
        rng = self._cluster_ranges()
        for k, (lo, hi) in rng.items():
            if lo <= yv <= hi:
                return k
        # fallback by sign
        if yv > 0.5:
            return 'wp'
        if yv < -0.5:
            return 'wn'
        return 'n'

    def _build_clusters(self, feats: torch.Tensor, labels: torch.Tensor) -> Dict[int, Dict]:
        """Build clusters using K-means, then assign cluster identity based on majority label range.

        Returns: dict mapping cluster-id -> {
            'mask': BoolTensor [N],
            'center': Tensor [D],
            'fine': str,
            'similar_mask': BoolTensor [N_cluster],  # 簇内相似实例的mask
            'dissimilar_mask': BoolTensor [N_cluster]  # 簇内不相似实例的mask
        }
        """
        device = feats.device

        # 使用K-means对所有特征进行聚类，最多7个簇
        n_samples = feats.size(0)
        if n_samples == 0:
            return {}

        k = min(7, n_samples)  # 最多7个簇
        cluster_labels, cluster_centers = self.kmeans(feats, k)

        clusters: Dict[int, Dict] = {}

        # 为每个K-means簇分配标识，基于占比最大的标签范围
        label_ranges = self._cluster_ranges()
        key_to_idx = self._cluster_index_map()

        for cluster_id in range(k):
            # 获取属于当前簇的样本
            cluster_mask = cluster_labels == cluster_id
            if not cluster_mask.any():
                continue

            cluster_feats = feats[cluster_mask]
            cluster_sample_labels = labels[cluster_mask]
            cluster_center = cluster_centers[cluster_id]

            # 统计每个标签范围的样本数量
            range_counts = {}
            for range_name, (low, high) in label_ranges.items():
                count = ((cluster_sample_labels >= low) & (cluster_sample_labels <= high)).sum().item()
                range_counts[range_name] = count

            # 找到占比最大的标签范围作为簇标识
            if sum(range_counts.values()) > 0:
                cluster_fine_label = max(range_counts.items(), key=lambda x: x[1])[0]
            else:
                # 如果没有样本落在任何范围内，使用平均值决定
                mean_label = cluster_sample_labels.mean().item()
                if mean_label > 0.5:
                    cluster_fine_label = 'wp'
                elif mean_label < -0.5:
                    cluster_fine_label = 'wn'
                else:
                    cluster_fine_label = 'n'

            # 确定簇内的相似和不相似实例
            cluster_range = label_ranges[cluster_fine_label]
            similar_mask_local = ((cluster_sample_labels >= cluster_range[0]) &
                                (cluster_sample_labels <= cluster_range[1]))
            dissimilar_mask_local = ~similar_mask_local

            # 存储簇信息
            cluster_idx = key_to_idx[cluster_fine_label]
            clusters[cluster_idx] = {
                'mask': cluster_mask,
                'center': cluster_center,
                'fine': cluster_fine_label,
                'similar_mask': similar_mask_local,
                'dissimilar_mask': dissimilar_mask_local,
                'cluster_feats': cluster_feats,
                'cluster_labels': cluster_sample_labels
            }

        return clusters

    def forward(self, feats: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """Compute emotion-aware contrastive loss.

        目标：相同细微情感标签范围的实例靠近，不同范围的实例远离

        feats: [N, D] - 特征向量
        labels: [N] - 情感标签值 (-3 to 3)
        """
        device = feats.device
        n_samples = feats.size(0)

        if n_samples == 0:
            return feats.new_tensor(0.0, requires_grad=True)

        # 为每个样本分配细微情感标签
        fine_labels = []
        for i in range(n_samples):
            fine_label = self._assign_fine_label(labels[i])
            fine_labels.append(fine_label)

        # 计算三项损失
        intra_loss = self._compute_intra_emotion_loss(feats, fine_labels)
        inter_loss = self._compute_inter_emotion_loss(feats, fine_labels)
        center_loss = self._compute_center_separation_loss(feats, fine_labels)

        # 总损失
        total_loss = intra_loss + self.v1 * inter_loss + self.v2 * center_loss
        return total_loss

    def _compute_intra_emotion_loss(self, feats: torch.Tensor, fine_labels: list) -> torch.Tensor:
        """计算相同细微情感标签实例之间的拉近损失 - 使用马氏距离和高效计算"""
        device = feats.device
        d = feats.size(1)
        I = torch.eye(d, device=device)
        intra_loss = feats.new_tensor(0.0)
        count = 0

        # 获取所有唯一的细微情感标签
        unique_labels = list(set(fine_labels))
        key_to_idx = self._cluster_index_map()

        for label in unique_labels:
            # 找到具有相同细微情感标签的实例
            indices = [i for i, l in enumerate(fine_labels) if l == label]
            if len(indices) < 2:
                continue

            same_emotion_feats = feats[indices]  # [n_same, D]
            n_same = same_emotion_feats.size(0)

            # 获取该情感对应的度量矩阵
            if label in key_to_idx:
                cid = key_to_idx[label]
                M = self._get_metric(cid) + self.beta * I
            else:
                M = I  # 默认使用单位矩阵

            # 高效计算：使用向量化操作而不是双重循环
            if n_same >= 2:
                # 限制计算的对数以控制复杂度
                max_pairs = min(self.max_pairs, n_same * (n_same - 1) // 2)
                if n_same * (n_same - 1) // 2 <= max_pairs:
                    # 计算所有对
                    ii, jj = torch.triu_indices(n_same, n_same, offset=1, device=device)
                else:
                    # 随机采样对
                    all_pairs = n_same * (n_same - 1) // 2
                    ii, jj = torch.triu_indices(n_same, n_same, offset=1, device=device)
                    sel = torch.randperm(all_pairs, device=device)[:max_pairs]
                    ii, jj = ii[sel], jj[sel]

                x1 = same_emotion_feats[ii]
                x2 = same_emotion_feats[jj]
                d_pairs = _sq_mahalanobis(x1, x2, M)
                intra_loss = intra_loss + d_pairs.mean()
                count += 1

        return intra_loss / max(count, 1)

    def _compute_inter_emotion_loss(self, feats: torch.Tensor, fine_labels: list) -> torch.Tensor:
        """计算不同细微情感标签实例之间的推远损失 - 高效采样版本"""
        device = feats.device
        inter_loss = feats.new_tensor(0.0)
        count = 0
        margin = 2.0  # 最小间距

        n_samples = len(fine_labels)

        # 为了避免O(N²)复杂度，使用采样策略
        max_inter_pairs = min(self.max_pairs * 2, n_samples * (n_samples - 1) // 2)

        if n_samples <= 50:  # 小batch直接计算所有对
            for i in range(n_samples):
                for j in range(i + 1, n_samples):
                    if fine_labels[i] != fine_labels[j]:
                        dist = torch.norm(feats[i] - feats[j], p=2)
                        loss_ij = torch.clamp(margin - dist, min=0.0)
                        inter_loss = inter_loss + loss_ij
                        count += 1
        else:  # 大batch使用采样
            # 随机采样不同情感的实例对
            sampled_pairs = 0
            attempts = 0
            max_attempts = max_inter_pairs * 3  # 避免无限循环

            while sampled_pairs < max_inter_pairs and attempts < max_attempts:
                i = torch.randint(0, n_samples, (1,)).item()
                j = torch.randint(0, n_samples, (1,)).item()
                attempts += 1

                if i != j and fine_labels[i] != fine_labels[j]:
                    dist = torch.norm(feats[i] - feats[j], p=2)
                    loss_ij = torch.clamp(margin - dist, min=0.0)
                    inter_loss = inter_loss + loss_ij
                    count += 1
                    sampled_pairs += 1

        return inter_loss / max(count, 1)

    def _compute_center_separation_loss(self, feats: torch.Tensor, fine_labels: list) -> torch.Tensor:
        """计算不同情感中心之间的分离损失 - 高效向量化版本"""
        center_loss = feats.new_tensor(0.0)
        margin = 3.0  # 中心间最小距离

        # 高效计算每个细微情感的中心
        unique_labels = list(set(fine_labels))
        if len(unique_labels) < 2:
            return center_loss

        centers = []
        center_labels = []

        for label in unique_labels:
            indices = torch.tensor([i for i, l in enumerate(fine_labels) if l == label],
                                 device=feats.device, dtype=torch.long)
            if len(indices) > 0:
                center = feats[indices].mean(dim=0)
                centers.append(center)
                center_labels.append(label)

        if len(centers) < 2:
            return center_loss

        # 向量化计算所有中心对的距离
        centers_tensor = torch.stack(centers)  # [n_centers, D]
        n_centers = centers_tensor.size(0)

        # 计算所有中心对的距离矩阵
        centers_expanded_i = centers_tensor.unsqueeze(1).expand(n_centers, n_centers, -1)
        centers_expanded_j = centers_tensor.unsqueeze(0).expand(n_centers, n_centers, -1)
        distances = torch.norm(centers_expanded_i - centers_expanded_j, p=2, dim=2)

        # 只考虑上三角矩阵（避免重复计算）
        mask = torch.triu(torch.ones(n_centers, n_centers, device=feats.device), diagonal=1).bool()
        valid_distances = distances[mask]

        # 应用margin loss
        margin_losses = torch.clamp(margin - valid_distances, min=0.0)
        center_loss = margin_losses.mean()

        return center_loss
