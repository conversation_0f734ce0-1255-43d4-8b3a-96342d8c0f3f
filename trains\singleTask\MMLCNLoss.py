import math
from typing import Dict, List, <PERSON><PERSON>

import torch
import torch.nn as nn
import torch.nn.functional as F


class _KMeans:
    """Simple KMeans in torch for small batches.

    Supports dynamic k (<= n) and returns labels and centroids.
    """

    def __init__(self, n_iters: int = 10):
        self.n_iters = n_iters

    @staticmethod
    def _init_centroids(x: torch.Tensor, k: int) -> torch.Tensor:
        # x: [N, D]
        n = x.size(0)
        if n == 0:
            return x.new_zeros((0, x.size(1)))
        if k >= n:
            # use unique points or pad repeats if needed
            if n == 1:
                return x.repeat(k, 1)
            idx = torch.randperm(n, device=x.device)[:k]
            return x[idx]
        idx = torch.randperm(n, device=x.device)[:k]
        return x[idx]

    @staticmethod
    def _pairwise_sqdist(x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
        # x: [N, D], y: [K, D] -> [N, K]
        x2 = (x * x).sum(dim=1, keepdim=True)
        y2 = (y * y).sum(dim=1).unsqueeze(0)
        xy = x @ y.t()
        return torch.clamp(x2 - 2 * xy + y2, min=0.0)

    def __call__(self, x: torch.Tensor, k: int) -> Tuple[torch.Tensor, torch.Tensor]:
        # returns labels [N] and centroids [k, D]
        n = x.size(0)
        if n == 0 or k <= 0:
            return x.new_zeros((n,), dtype=torch.long), x.new_zeros((0, x.size(1)))
        k = min(k, n)
        c = self._init_centroids(x, k)
        labels = torch.zeros(n, dtype=torch.long, device=x.device)
        for _ in range(self.n_iters):
            d = self._pairwise_sqdist(x, c)  # [N, k]
            new_labels = torch.argmin(d, dim=1)
            if torch.equal(labels, new_labels):
                break
            labels = new_labels
            # recompute centroids
            for i in range(k):
                mask = labels == i
                if mask.any():
                    c[i] = x[mask].mean(dim=0)
                else:
                    # reinit empty cluster to random point
                    ridx = torch.randint(0, n, (1,), device=x.device)
                    c[i] = x[ridx]
        return labels, c


def _sq_mahalanobis(x: torch.Tensor, y: torch.Tensor, M: torch.Tensor) -> torch.Tensor:
    """Squared Mahalanobis: (x - y)^T M (x - y)
    x: [N, D] or [D], y: broadcastable to x, M: [D, D]
    returns: [N]
    """
    diff = x - y
    # [N, D] @ [D, D] -> [N, D]
    Md = diff @ M
    return (Md * diff).sum(dim=-1)


class MetricClusterHingeLoss(nn.Module):
    """Implements formula-10 for multi-modal sentiment clustering with local metrics.

    - Builds up to 7 clusters per batch: 3 negative, 1 neutral, 3 positive.
    - Learns one PSD metric per fine-grained cluster via L_i L_i^T.
    - Triplet-like hinge over cluster centers enforces separation with adaptive margins.

    Inputs:
    - feats: Tensor [N, D] features from shared encoder (stacked across modalities)
    - labels: Tensor [N] real-valued sentiment scores in [-3, 3]

    Hyperparams (with defaults if missing in args):
    - beta: 1e-3, v1: 1.0, v2: 1.0, alpha: 0.3, kmeans_iters: 10, 
      max_pairs_per_cluster: 256 (to bound O(N^2) term cost)
    """

    def __init__(self, feature_dim: int = None, beta: float = 1e-3, v1: float = 10.0, v2: float = 10.0,
                 alpha: float = 0.3, kmeans_iters: int = 10, max_pairs_per_cluster: int = 1024):
        super().__init__()
        self.beta = beta
        self.v1 = v1
        self.v2 = v2
        self.alpha = alpha
        self.kmeans = _KMeans(n_iters=kmeans_iters)
        self.max_pairs = max_pairs_per_cluster
        self.feature_dim = feature_dim

        # We maintain 7 learnable local metrics L_i (lower-triangular via free matrix), M_i = L_i L_i^T
        self.num_clusters = 7
        if feature_dim is not None:
            self._init_metrics(feature_dim)
        else:
            self.L_params = None  # lazy init on first forward

    def _init_metrics(self, d: int, device: torch.device = None):
        Ls = []
        for _ in range(self.num_clusters):
            # small init near identity
            if device is None:
                L = torch.eye(d) * 0.1
                L += 0.01 * torch.randn(d, d)
            else:
                L = torch.eye(d, device=device) * 0.1
                L += 0.01 * torch.randn(d, d, device=device)
            Ls.append(nn.Parameter(L))
        self.L_params = nn.ParameterList(Ls)
        self.feature_dim = d

    def _get_metric(self, idx: int) -> torch.Tensor:
        L = self.L_params[idx]
        M = L @ L.t()
        # add tiny jitter for stability
        return M

    @staticmethod
    def _cluster_ranges() -> Dict[str, Tuple[float, float]]:
        # fine-grained ranges for mapping clusters
        return {
            'wn': (-1.5, -0.5),
            'n': (-0.5, 0.5),
            'wp': (0.5, 1.5),
            'np': (1.5, 2.5),
            'hp': (2.5, 3.1),  # upper bound inclusive
            'nn': (-2.5, -1.5),
            'hn': (-3.1, -2.5),
        }

    @staticmethod
    def _cluster_index_map() -> Dict[str, int]:
        # fixed ordering of 7 clusters
        # 0: highly negative, 1: negative, 2: weakly negative, 3: neutral,
        # 4: weakly positive, 5: positive, 6: highly positive
        return {'hn': 0, 'nn': 1, 'wn': 2, 'n': 3, 'wp': 4, 'np': 5, 'hp': 6}

    def _assign_fine_label(self, y: torch.Tensor) -> str:
        yv = y.item()
        rng = self._cluster_ranges()
        for k, (lo, hi) in rng.items():
            if lo <= yv <= hi:
                return k
        # fallback by sign
        if yv > 0.5:
            return 'wp'
        if yv < -0.5:
            return 'wn'
        return 'n'

    def _build_clusters(self, feats: torch.Tensor, labels: torch.Tensor) -> Dict[int, Dict]:
        """Build clusters using K-means, then assign cluster identity based on majority label range.

        Returns: dict mapping cluster-id -> {
            'mask': BoolTensor [N],
            'center': Tensor [D],
            'fine': str,
            'similar_mask': BoolTensor [N_cluster],  # 簇内相似实例的mask
            'dissimilar_mask': BoolTensor [N_cluster]  # 簇内不相似实例的mask
        }
        """
        device = feats.device

        # 使用K-means对所有特征进行聚类，最多7个簇
        n_samples = feats.size(0)
        if n_samples == 0:
            return {}

        k = min(7, n_samples)  # 最多7个簇
        cluster_labels, cluster_centers = self.kmeans(feats, k)

        clusters: Dict[int, Dict] = {}

        # 为每个K-means簇分配标识，基于占比最大的标签范围
        label_ranges = self._cluster_ranges()
        key_to_idx = self._cluster_index_map()

        for cluster_id in range(k):
            # 获取属于当前簇的样本
            cluster_mask = cluster_labels == cluster_id
            if not cluster_mask.any():
                continue

            cluster_feats = feats[cluster_mask]
            cluster_sample_labels = labels[cluster_mask]
            cluster_center = cluster_centers[cluster_id]

            # 统计每个标签范围的样本数量
            range_counts = {}
            for range_name, (low, high) in label_ranges.items():
                count = ((cluster_sample_labels >= low) & (cluster_sample_labels <= high)).sum().item()
                range_counts[range_name] = count

            # 找到占比最大的标签范围作为簇标识
            if sum(range_counts.values()) > 0:
                cluster_fine_label = max(range_counts.items(), key=lambda x: x[1])[0]
            else:
                # 如果没有样本落在任何范围内，使用平均值决定
                mean_label = cluster_sample_labels.mean().item()
                if mean_label > 0.5:
                    cluster_fine_label = 'wp'
                elif mean_label < -0.5:
                    cluster_fine_label = 'wn'
                else:
                    cluster_fine_label = 'n'

            # 确定簇内的相似和不相似实例
            cluster_range = label_ranges[cluster_fine_label]
            similar_mask_local = ((cluster_sample_labels >= cluster_range[0]) &
                                (cluster_sample_labels <= cluster_range[1]))
            dissimilar_mask_local = ~similar_mask_local

            # 存储簇信息
            cluster_idx = key_to_idx[cluster_fine_label]
            clusters[cluster_idx] = {
                'mask': cluster_mask,
                'center': cluster_center,
                'fine': cluster_fine_label,
                'similar_mask': similar_mask_local,
                'dissimilar_mask': dissimilar_mask_local,
                'cluster_feats': cluster_feats,
                'cluster_labels': cluster_sample_labels
            }

        return clusters

    def forward(self, feats: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """Compute MMLCN loss according to the exact formula:

        min_{M_1,...,M_l} Σ_i Σ_{(j,k)∈S_i} d_{M_i+βI}(x_j,x_k)
        + v_1 Σ_i Σ_{(j,k)∈D_i} d_{(M_i+βI)^{-1}}(x_j,x_k)
        + v_2 Σ_i Σ_{j≠i} Σ_{(k,i,j)∈T_{ij}} ξ_{ijk}

        feats: [N, D] - 特征向量
        labels: [N] - 情感标签值 (-3 to 3)
        """
        if self.L_params is None:
            self._init_metrics(feats.size(1), device=feats.device)

        device = feats.device
        d = feats.size(1)
        I = torch.eye(d, device=device)

        # 构建细微情感聚类
        clusters = self._build_emotion_clusters(feats, labels)
        if len(clusters) == 0:
            return feats.new_tensor(0.0, requires_grad=True)

        # 计算三项损失
        term1 = self._compute_similar_pairs_loss(feats, clusters, I)      # 第一项：S_i中的相似对
        term2 = self._compute_dissimilar_pairs_loss(feats, clusters, I)   # 第二项：D_i中的不相似对
        term3 = self._compute_triplet_hinge_loss(feats, clusters, I)      # 第三项：三元组铰链损失

        # 按公式组合
        total_loss = term1 + self.v1 * term2 + self.v2 * term3
        return total_loss

    def _build_emotion_clusters(self, feats: torch.Tensor, labels: torch.Tensor) -> dict:
        """构建基于细微情感标签的聚类"""
        device = feats.device
        n_samples = feats.size(0)

        # 为每个样本分配细微情感标签
        fine_labels = []
        for i in range(n_samples):
            fine_label = self._assign_fine_label(labels[i])
            fine_labels.append(fine_label)

        # 按细微情感标签分组
        emotion_groups = {}
        for i, fine_label in enumerate(fine_labels):
            if fine_label not in emotion_groups:
                emotion_groups[fine_label] = []
            emotion_groups[fine_label].append(i)

        # 构建聚类信息
        clusters = {}
        key_to_idx = self._cluster_index_map()
        label_ranges = self._cluster_ranges()

        for fine_label, indices in emotion_groups.items():
            if len(indices) == 0:
                continue

            indices_tensor = torch.tensor(indices, device=device, dtype=torch.long)
            cluster_feats = feats[indices_tensor]
            cluster_labels = labels[indices_tensor]

            # 计算聚类中心
            center = cluster_feats.mean(dim=0)

            # 确定相似和不相似实例
            range_low, range_high = label_ranges[fine_label]
            similar_mask = ((cluster_labels >= range_low) & (cluster_labels <= range_high))
            dissimilar_mask = ~similar_mask

            # 存储聚类信息
            if fine_label in key_to_idx:
                cid = key_to_idx[fine_label]
                clusters[cid] = {
                    'fine': fine_label,
                    'indices': indices_tensor,
                    'feats': cluster_feats,
                    'labels': cluster_labels,
                    'center': center,
                    'similar_mask': similar_mask,
                    'dissimilar_mask': dissimilar_mask
                }

        return clusters

    def _compute_similar_pairs_loss(self, feats: torch.Tensor, clusters: dict, I: torch.Tensor) -> torch.Tensor:
        """计算第一项：Σ_i Σ_{(j,k)∈S_i} d_{M_i+βI}(x_j,x_k)"""
        total_loss = feats.new_tensor(0.0)
        count = 0

        for cid, cluster_info in clusters.items():
            similar_mask = cluster_info['similar_mask']
            cluster_feats = cluster_info['feats']

            if not similar_mask.any():
                continue

            # 获取相似实例
            similar_feats = cluster_feats[similar_mask]
            n_similar = similar_feats.size(0)

            if n_similar < 2:
                continue

            # 获取度量矩阵 M_i + βI
            M = self._get_metric(cid) + self.beta * I

            # 计算相似对的距离
            max_pairs = min(self.max_pairs, n_similar * (n_similar - 1) // 2)
            ii, jj = torch.triu_indices(n_similar, n_similar, offset=1, device=feats.device)

            if ii.numel() > max_pairs:
                sel = torch.randperm(ii.numel(), device=feats.device)[:max_pairs]
                ii, jj = ii[sel], jj[sel]

            x1, x2 = similar_feats[ii], similar_feats[jj]
            distances = _sq_mahalanobis(x1, x2, M)
            total_loss = total_loss + distances.sum()
            count += distances.numel()

        return total_loss / max(count, 1)

    def _compute_dissimilar_pairs_loss(self, feats: torch.Tensor, clusters: dict, I: torch.Tensor) -> torch.Tensor:
        """计算第二项：v_1 Σ_i Σ_{(j,k)∈D_i} d_{(M_i+βI)^{-1}}(x_j,x_k)"""
        total_loss = feats.new_tensor(0.0)
        count = 0

        for cid, cluster_info in clusters.items():
            dissimilar_mask = cluster_info['dissimilar_mask']
            cluster_feats = cluster_info['feats']

            if not dissimilar_mask.any():
                continue

            # 获取不相似实例
            dissimilar_feats = cluster_feats[dissimilar_mask]
            n_dissimilar = dissimilar_feats.size(0)

            if n_dissimilar < 2:
                continue

            # 获取逆度量矩阵 (M_i + βI)^{-1}
            M = self._get_metric(cid) + self.beta * I
            try:
                M_inv = torch.linalg.inv(M)
            except RuntimeError:
                M_inv = torch.linalg.pinv(M)

            # 计算不相似对的距离
            max_pairs = min(self.max_pairs, n_dissimilar * (n_dissimilar - 1) // 2)
            ii, jj = torch.triu_indices(n_dissimilar, n_dissimilar, offset=1, device=feats.device)

            if ii.numel() > max_pairs:
                sel = torch.randperm(ii.numel(), device=feats.device)[:max_pairs]
                ii, jj = ii[sel], jj[sel]

            x1, x2 = dissimilar_feats[ii], dissimilar_feats[jj]
            distances = _sq_mahalanobis(x1, x2, M_inv)
            total_loss = total_loss + distances.sum()
            count += distances.numel()

        return total_loss / max(count, 1)

    def _compute_triplet_hinge_loss(self, feats: torch.Tensor, clusters: dict, I: torch.Tensor) -> torch.Tensor:
        """计算第三项：v_2 Σ_i Σ_{j≠i} Σ_{(k,i,j)∈T_{ij}} ξ_{ijk}

        约束：d_{M_j+βI}(x_k,c_j) - d_{M_i+βI}(x_k,c_i) ≥ ρ_{ij} - ξ_{ijk}
        """
        total_loss = feats.new_tensor(0.0)
        count = 0

        cluster_ids = list(clusters.keys())
        if len(cluster_ids) < 2:
            return total_loss

        # 计算所有聚类中心
        centers = {}
        for cid, cluster_info in clusters.items():
            centers[cid] = cluster_info['center']

        # 对于每对聚类 (i, j)
        for i_idx, cid_i in enumerate(cluster_ids):
            for j_idx, cid_j in enumerate(cluster_ids):
                if cid_i == cid_j:
                    continue

                cluster_i = clusters[cid_i]
                cluster_j = clusters[cid_j]
                center_i = centers[cid_i]
                center_j = centers[cid_j]

                # 获取度量矩阵
                M_i = self._get_metric(cid_i) + self.beta * I
                M_j = self._get_metric(cid_j) + self.beta * I

                # 自适应边界 ρ_{ij} = α * ||c_i - c_j||
                rho_ij = self.alpha * torch.norm(center_i - center_j, p=2)

                # 对于聚类i中的每个点k
                cluster_i_feats = cluster_i['feats']
                n_points = cluster_i_feats.size(0)

                # 限制计算的点数以控制复杂度
                max_points = min(self.max_pairs // len(cluster_ids), n_points)
                if n_points > max_points:
                    selected_indices = torch.randperm(n_points, device=feats.device)[:max_points]
                    selected_feats = cluster_i_feats[selected_indices]
                else:
                    selected_feats = cluster_i_feats

                for k in range(selected_feats.size(0)):
                    x_k = selected_feats[k]

                    # 计算距离
                    d_ki = _sq_mahalanobis(x_k.unsqueeze(0), center_i.unsqueeze(0), M_i).squeeze()
                    d_kj = _sq_mahalanobis(x_k.unsqueeze(0), center_j.unsqueeze(0), M_j).squeeze()

                    # 铰链损失：ξ_{ijk} = max(0, ρ_{ij} - (d_kj - d_ki))
                    xi_ijk = torch.clamp(rho_ij - (d_kj - d_ki), min=0.0)
                    total_loss = total_loss + xi_ijk
                    count += 1

        return total_loss / max(count, 1)
