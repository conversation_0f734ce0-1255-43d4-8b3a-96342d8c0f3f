import math
from typing import Dict, List, <PERSON><PERSON>

import torch
import torch.nn as nn
import torch.nn.functional as F


class _KMeans:
    """Simple KMeans in torch for small batches.

    Supports dynamic k (<= n) and returns labels and centroids.
    """

    def __init__(self, n_iters: int = 10):
        self.n_iters = n_iters

    @staticmethod
    def _init_centroids(x: torch.Tensor, k: int) -> torch.Tensor:
        # x: [N, D]
        n = x.size(0)
        if n == 0:
            return x.new_zeros((0, x.size(1)))
        if k >= n:
            # use unique points or pad repeats if needed
            if n == 1:
                return x.repeat(k, 1)
            idx = torch.randperm(n, device=x.device)[:k]
            return x[idx]
        idx = torch.randperm(n, device=x.device)[:k]
        return x[idx]

    @staticmethod
    def _pairwise_sqdist(x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
        # x: [N, D], y: [K, D] -> [N, K]
        x2 = (x * x).sum(dim=1, keepdim=True)
        y2 = (y * y).sum(dim=1).unsqueeze(0)
        xy = x @ y.t()
        return torch.clamp(x2 - 2 * xy + y2, min=0.0)

    def __call__(self, x: torch.Tensor, k: int) -> Tuple[torch.Tensor, torch.Tensor]:
        # returns labels [N] and centroids [k, D]
        n = x.size(0)
        if n == 0 or k <= 0:
            return x.new_zeros((n,), dtype=torch.long), x.new_zeros((0, x.size(1)))
        k = min(k, n)
        c = self._init_centroids(x, k)
        labels = torch.zeros(n, dtype=torch.long, device=x.device)
        for _ in range(self.n_iters):
            d = self._pairwise_sqdist(x, c)  # [N, k]
            new_labels = torch.argmin(d, dim=1)
            if torch.equal(labels, new_labels):
                break
            labels = new_labels
            # recompute centroids
            for i in range(k):
                mask = labels == i
                if mask.any():
                    c[i] = x[mask].mean(dim=0)
                else:
                    # reinit empty cluster to random point
                    ridx = torch.randint(0, n, (1,), device=x.device)
                    c[i] = x[ridx]
        return labels, c


def _sq_mahalanobis(x: torch.Tensor, y: torch.Tensor, M: torch.Tensor) -> torch.Tensor:
    """Squared Mahalanobis: (x - y)^T M (x - y)
    x: [N, D] or [D], y: broadcastable to x, M: [D, D]
    returns: [N]
    """
    diff = x - y
    # [N, D] @ [D, D] -> [N, D]
    Md = diff @ M
    return (Md * diff).sum(dim=-1)


class MetricClusterHingeLoss(nn.Module):
    """Implements formula-10 for multi-modal sentiment clustering with local metrics.

    - Builds up to 7 clusters per batch: 3 negative, 1 neutral, 3 positive.
    - Learns one PSD metric per fine-grained cluster via L_i L_i^T.
    - Triplet-like hinge over cluster centers enforces separation with adaptive margins.

    Inputs:
    - feats: Tensor [N, D] features from shared encoder (stacked across modalities)
    - labels: Tensor [N] real-valued sentiment scores in [-3, 3]

    Hyperparams (with defaults if missing in args):
    - beta: 1e-3, v1: 1.0, v2: 1.0, alpha: 0.3, kmeans_iters: 10, 
      max_pairs_per_cluster: 256 (to bound O(N^2) term cost)
    """

    def __init__(self, feature_dim: int = None, beta: float = 1e-3, v1: float = 1.0, v2: float = 1.0,
                 alpha: float = 0.3, kmeans_iters: int = 100, max_pairs_per_cluster: int = 1024):
        super().__init__()
        self.beta = beta
        self.v1 = v1
        self.v2 = v2
        self.alpha = alpha
        self.kmeans = _KMeans(n_iters=kmeans_iters)
        self.max_pairs = max_pairs_per_cluster
        self.feature_dim = feature_dim

        # We maintain 7 learnable local metrics L_i (lower-triangular via free matrix), M_i = L_i L_i^T
        self.num_clusters = 7
        if feature_dim is not None:
            self._init_metrics(feature_dim)
        else:
            self.L_params = None  # lazy init on first forward

    def _init_metrics(self, d: int, device: torch.device = None):
        Ls = []
        for _ in range(self.num_clusters):
            # small init near identity
            if device is None:
                L = torch.eye(d) * 0.1
                L += 0.01 * torch.randn(d, d)
            else:
                L = torch.eye(d, device=device) * 0.1
                L += 0.01 * torch.randn(d, d, device=device)
            Ls.append(nn.Parameter(L))
        self.L_params = nn.ParameterList(Ls)
        self.feature_dim = d

    def _get_metric(self, idx: int) -> torch.Tensor:
        L = self.L_params[idx]
        M = L @ L.t()
        # add tiny jitter for stability
        return M

    @staticmethod
    def _cluster_ranges() -> Dict[str, Tuple[float, float]]:
        # fine-grained ranges for mapping clusters
        return {
            'wn': (-1.5, -0.5),
            'n': (-0.5, 0.5),
            'wp': (0.5, 1.5),
            'np': (1.5, 2.5),
            'hp': (2.5, 3.1),  # upper bound inclusive
            'nn': (-2.5, -1.5),
            'hn': (-3.1, -2.5),
        }

    @staticmethod
    def _cluster_index_map() -> Dict[str, int]:
        # fixed ordering of 7 clusters
        # 0: highly negative, 1: negative, 2: weakly negative, 3: neutral,
        # 4: weakly positive, 5: positive, 6: highly positive
        return {'hn': 0, 'nn': 1, 'wn': 2, 'n': 3, 'wp': 4, 'np': 5, 'hp': 6}

    def _assign_fine_label(self, y: torch.Tensor) -> str:
        yv = y.item()
        rng = self._cluster_ranges()
        for k, (lo, hi) in rng.items():
            if lo <= yv <= hi:
                return k
        # fallback by sign
        if yv > 0.5:
            return 'wp'
        if yv < -0.5:
            return 'wn'
        return 'n'

    def _build_clusters(self, feats: torch.Tensor, labels: torch.Tensor) -> Dict[int, Dict]:
        """Build fine-grained clusters per batch.

        Returns: dict mapping cluster-id -> {
            'mask': BoolTensor [N],
            'center': Tensor [D],
            'fine': str
        }
        """
        device = feats.device
        idx_all = torch.arange(feats.size(0), device=device)

        pos_mask = (labels >= 0.5) & (labels <= 3.0)
        neu_mask = (labels > -0.5) & (labels < 0.5)
        neg_mask = (labels >= -3.0) & (labels <= -0.5)

        clusters: Dict[int, Dict] = {}

        # helper to map kmeans clusters to fine-grained labels by majority label-range votes
        def map_subclusters(x: torch.Tensor, y: torch.Tensor, idxs: torch.Tensor, base_keys: List[str]) -> List[Tuple[str, torch.Tensor, torch.Tensor]]:
            if x.size(0) == 0:
                return []
            k = min(len(base_keys), max(1, x.size(0)))
            labels_local, cents = self.kmeans(x, k)
            # vote by ranges
            rng = self._cluster_ranges()
            used = set()
            mapped: List[Tuple[str, torch.Tensor, torch.Tensor]] = []
            for c in range(k):
                mask = labels_local == c
                xs = x[mask]
                ys = y[mask]
                gidx = idxs[mask]
                if xs.size(0) == 0:
                    continue
                counts = {bk: 0 for bk in base_keys}
                for v in ys:
                    # map numeric label to the three relevant keys for pos/neg
                    key = self._assign_fine_label(v)
                    if key in counts:
                        counts[key] += 1
                # pick by max count, tie-breaker by mean label
                if sum(counts.values()) == 0:
                    # fallback
                    mean_y = ys.mean().item()
                    if 'wp' in base_keys:
                        key = 'wp' if mean_y < 2.0 else ('np' if mean_y < 2.7 else 'hp')
                    else:
                        key = 'wn' if mean_y > -2.0 else ('nn' if mean_y > -2.7 else 'hn')
                else:
                    key = max(counts.items(), key=lambda kv: kv[1])[0]
                # ensure uniqueness across mapped
                if key in used:
                    # assign remaining by closeness of centroid mean label
                    mean_y = ys.mean().item()
                    for alt in base_keys:
                        if alt not in used:
                            key = alt
                            break
                used.add(key)
                mapped.append((key, xs, gidx))
            return mapped

        # negative group -> keys: ['hn','nn','wn'] (high, normal, weak)
        x_neg, y_neg = feats[neg_mask], labels[neg_mask]
        idx_neg = idx_all[neg_mask]
        neg_mapped = map_subclusters(x_neg, y_neg, idx_neg, ['wn', 'nn', 'hn'])
        # neutral
        x_neu, y_neu = feats[neu_mask], labels[neu_mask]
        idx_neu = idx_all[neu_mask]
        neu_clusters = []
        if x_neu.size(0) > 0:
            neu_clusters.append(('n', x_neu, idx_neu))
        # positive group -> keys: ['wp','np','hp']
        x_pos, y_pos = feats[pos_mask], labels[pos_mask]
        idx_pos = idx_all[pos_mask]
        pos_mapped = map_subclusters(x_pos, y_pos, idx_pos, ['wp', 'np', 'hp'])

        # aggregate and compute centers
        key_to_idx = self._cluster_index_map()
        for key, xs, gidx in (neg_mapped + neu_clusters + pos_mapped):
            if xs.size(0) == 0:
                continue
            center = xs.mean(dim=0, keepdim=True)  # [1, D]
            gid = key_to_idx[key]
            # mask directly from global indices
            mask = torch.zeros(feats.size(0), dtype=torch.bool, device=device)
            mask[gidx] = True
            clusters[gid] = {
                'mask': mask,
                'center': center.squeeze(0),
                'fine': key,
            }

        return clusters

    def _determine_cluster_identity(self, cluster_labels: torch.Tensor) -> str:
        """根据簇内样本标签的占比最大的范围确定簇标识"""
        ranges = self._cluster_ranges()
        range_counts = {}

        for range_key, (low, high) in ranges.items():
            count = ((cluster_labels >= low) & (cluster_labels <= high)).sum().item()
            range_counts[range_key] = count

        # 返回占比最大的范围标识
        if sum(range_counts.values()) == 0:
            return 'n'  # 默认为中性

        return max(range_counts.items(), key=lambda x: x[1])[0]

    def _is_similar_instance(self, label_value: float, cluster_identity: str) -> bool:
        """判断实例是否为相似实例（标签值符合簇标识的范围）"""
        ranges = self._cluster_ranges()
        if cluster_identity not in ranges:
            return False

        low, high = ranges[cluster_identity]
        return low <= label_value <= high

    def forward(self, feats: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """改进的MMLCN损失：区分相似和不相似实例进行差异化处理

        feats: [N, D] - 特征矩阵
        labels: [N] - 标签值 [-3, 3]
        """
        if self.L_params is None:
            self._init_metrics(feats.size(1), device=feats.device)

        device = feats.device
        d = feats.size(1)
        I = torch.eye(d, device=device)

        clusters = self._build_clusters(feats, labels)
        if len(clusters) == 0:
            return feats.new_tensor(0.0, requires_grad=True)

        total_similar_attract = feats.new_tensor(0.0)  # 相似实例吸引损失
        total_dissimilar_repel = feats.new_tensor(0.0)  # 不相似实例排斥损失
        total_inter_cluster = feats.new_tensor(0.0)     # 簇间分离损失
        cnt_similar = 0
        cnt_dissimilar = 0
        cnt_inter = 0

        for cid in active_ids:
            info = clusters[cid]
            mask = info['mask']
            Xi = feats[mask]  # [Ni, D]
            Ni = Xi.size(0)
            if Ni == 0:
                continue
            M = self._get_metric(cid) + self.beta * I
            # term 1: within-cluster similar pairs under M
            if Ni >= 2:
                # sample up to max_pairs pairs
                # create pair indices i<j
                idx = torch.arange(Ni, device=device)
                ii, jj = torch.triu_indices(Ni, Ni, offset=1, device=device)
                if ii.numel() > self.max_pairs:
                    sel = torch.randperm(ii.numel(), device=device)[: self.max_pairs]
                    ii, jj = ii[sel], jj[sel]
                x1 = Xi[ii]
                x2 = Xi[jj]
                d_ij = _sq_mahalanobis(x1, x2, M)
                total_sim = total_sim + d_ij.mean()
                cnt_sim += 1

            # term 2: dissimilar pairs under inverse metric
            # sample negatives from outside this cluster
            neg_mask = ~mask
            Xo = feats[neg_mask]
            No = Xo.size(0)
            if No > 0:
                # negatives: pair each point in Xi with a random subset of outside points
                k_neg = min(No, max(1, 2))
                sel = torch.randperm(No, device=device)[:k_neg]
                Xo_sel = Xo[sel]  # [k_neg, D]
                # compute inverse metric
                try:
                    Minv = torch.linalg.inv(M)
                except RuntimeError:
                    Minv = torch.linalg.pinv(M)
                # pair all Xi to all Xo_sel
                x1 = Xi.unsqueeze(1).expand(Ni, k_neg, d).reshape(-1, d)
                x2 = Xo_sel.unsqueeze(0).expand(Ni, k_neg, d).reshape(-1, d)
                d_io = _sq_mahalanobis(x1, x2, Minv)
                total_dis = total_dis + d_io.mean()
                cnt_dis += 1

        # term 3: triplet-like over centers
        for i in active_ids:
            Pi_mask = clusters[i]['mask']
            Xi = feats[Pi_mask]
            Ni = Xi.size(0)
            if Ni == 0:
                continue
            Mi = self._get_metric(i) + self.beta * I
            ci = centers[i]
            for j in active_ids:
                if j == i:
                    continue
                Mj = self._get_metric(j) + self.beta * I
                cj = centers[j]
                # adaptive margin rho_ij = alpha * ||ci - cj||
                rho_ij = self.alpha * torch.norm(ci - cj, p=2)
                # compute distances for all xk in Pi
                d_pos = _sq_mahalanobis(Xi, ci, Mi)  # [Ni]
                d_neg = _sq_mahalanobis(Xi, cj, Mj)  # [Ni]
                # l(z)=max(-z,0) with z = d_neg - d_pos - rho -> penalty when d_neg - d_pos < rho
                trip = F.relu(rho_ij + d_pos - d_neg)
                total_trip = total_trip + trip.mean()
                cnt_trip += 1

        # normalize and weight
        sim_term = total_sim / max(cnt_sim, 1)
        dis_term = total_dis / max(cnt_dis, 1)
        trip_term = total_trip / max(cnt_trip, 1)
        loss = sim_term + self.v1 * dis_term + self.v2 * trip_term
        return loss
