import math
from typing import Dict, List, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F





def _sq_mahalanobis(x: torch.Tensor, y: torch.Tensor, M: torch.Tensor) -> torch.Tensor:
    """Squared Mahalanobis: (x - y)^T M (x - y)
    x: [N, D] or [D], y: broadcastable to x, M: [D, D]
    returns: [N]
    """
    diff = x - y
    # [N, D] @ [D, D] -> [N, D]
    Md = diff @ M
    return (Md * diff).sum(dim=-1)


class MetricClusterHingeLoss(nn.Module):
    """Hierarchical emotion separation loss with independent metrics for each fine emotion.

    - Directly groups samples by fine emotion labels: hn, nn, wn, n, wp, np, hp
    - Learns one PSD metric per fine emotion via L_i L_i^T
    - Implements three-level hierarchical separation:
      1. Intra-emotion attraction: same fine emotions cluster together
      2. Fine emotion separation: 7 fine emotions separate from each other
      3. Macro emotion separation: negative, neutral, positive categories separate

    Inputs:
    - feats: Tensor [N, D] features from shared encoder (stacked across modalities)
    - labels: Tensor [N] real-valued sentiment scores in [-3, 3]

    Hyperparams:
    - beta: 1e-3 (PSD regularization)
    - v1: 10.0 (fine emotion separation weight)
    - v2: 10.0 (macro emotion separation weight)
    - alpha: 0.3 (adaptive margin coefficient)
    - max_pairs_per_cluster: 1024 (to bound O(N^2) term cost)
    """

    def __init__(self, feature_dim: int = None, beta: float = 1e-3, v1: float = 10.0, v2: float = 10.0,
                 alpha: float = 0.3, max_pairs_per_cluster: int = 1024):
        super().__init__()
        self.beta = beta
        self.v1 = v1
        self.v2 = v2
        self.alpha = alpha
        self.max_pairs = max_pairs_per_cluster
        self.feature_dim = feature_dim

        # We maintain 7 learnable local metrics L_i (lower-triangular via free matrix), M_i = L_i L_i^T
        self.num_clusters = 7
        if feature_dim is not None:
            self._init_metrics(feature_dim)
        else:
            self.L_params = None  # lazy init on first forward

    def _init_metrics(self, d: int, device: torch.device = None):
        Ls = []
        for _ in range(self.num_clusters):
            # small init near identity
            if device is None:
                L = torch.eye(d) * 0.1
                L += 0.01 * torch.randn(d, d)
            else:
                L = torch.eye(d, device=device) * 0.1
                L += 0.01 * torch.randn(d, d, device=device)
            Ls.append(nn.Parameter(L))
        self.L_params = nn.ParameterList(Ls)
        self.feature_dim = d

    def _get_metric(self, idx: int) -> torch.Tensor:
        L = self.L_params[idx]
        M = L @ L.t()
        # add tiny jitter for stability
        return M

    @staticmethod
    def _cluster_ranges() -> Dict[str, Tuple[float, float]]:
        # fine-grained ranges for mapping clusters
        return {
            'wn': (-1.5, -0.5),
            'n': (-0.5, 0.5),
            'wp': (0.5, 1.5),
            'np': (1.5, 2.5),
            'hp': (2.5, 3.1),  # upper bound inclusive
            'nn': (-2.5, -1.5),
            'hn': (-3.1, -2.5),
        }

    @staticmethod
    def _cluster_index_map() -> Dict[str, int]:
        # fixed ordering of 7 clusters
        # 0: highly negative, 1: negative, 2: weakly negative, 3: neutral,
        # 4: weakly positive, 5: positive, 6: highly positive
        return {'hn': 0, 'nn': 1, 'wn': 2, 'n': 3, 'wp': 4, 'np': 5, 'hp': 6}

    def _assign_fine_label(self, y: torch.Tensor) -> str:
        yv = y.item()
        rng = self._cluster_ranges()
        for k, (lo, hi) in rng.items():
            if lo <= yv <= hi:
                return k
        # fallback by sign
        if yv > 0.5:
            return 'wp'
        if yv < -0.5:
            return 'wn'
        return 'n'



    def forward(self, feats: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """Compute hierarchical emotion separation loss:

        1. 宏观层次：积极、中立、消极三大类相互远离
        2. 细微层次：7种细微情感相互远离
        3. 同类聚合：相同细微情感的样本相互靠近

        feats: [N, D] - 特征向量
        labels: [N] - 情感标签值 (-3 to 3)
        """
        if self.L_params is None:
            self._init_metrics(feats.size(1), device=feats.device)

        device = feats.device
        d = feats.size(1)
        I = torch.eye(d, device=device)

        # 构建层次化情感结构
        emotion_structure = self._build_hierarchical_emotions(feats, labels)
        if not emotion_structure:
            return feats.new_tensor(0.0, requires_grad=True)

        # 计算层次化损失
        intra_loss = self._compute_intra_emotion_attraction(emotion_structure, I)     # 同类细微情感聚合
        fine_loss = self._compute_fine_emotion_separation(emotion_structure)         # 细微情感间分离
        macro_loss = self._compute_macro_emotion_separation(emotion_structure)       # 宏观情感类分离

        # 组合损失
        total_loss = intra_loss + self.v1 * fine_loss + self.v2 * macro_loss
        return total_loss

    def _build_hierarchical_emotions(self, feats: torch.Tensor, labels: torch.Tensor) -> dict:
        """构建层次化情感结构"""
        device = feats.device
        n_samples = feats.size(0)

        # 为每个样本分配细微情感标签
        fine_labels = []
        macro_labels = []
        for i in range(n_samples):
            fine_label = self._assign_fine_label(labels[i])
            fine_labels.append(fine_label)

            # 分配宏观情感标签
            if fine_label in ['hn', 'nn', 'wn']:
                macro_labels.append('negative')
            elif fine_label == 'n':
                macro_labels.append('neutral')
            else:  # wp, np, hp
                macro_labels.append('positive')

        # 构建细微情感组
        fine_emotion_groups = {}
        for i, fine_label in enumerate(fine_labels):
            if fine_label not in fine_emotion_groups:
                fine_emotion_groups[fine_label] = []
            fine_emotion_groups[fine_label].append(i)

        # 构建宏观情感组
        macro_emotion_groups = {}
        for i, macro_label in enumerate(macro_labels):
            if macro_label not in macro_emotion_groups:
                macro_emotion_groups[macro_label] = []
            macro_emotion_groups[macro_label].append(i)

        # 构建层次化结构
        emotion_structure = {
            'fine_emotions': {},
            'macro_emotions': {},
            'fine_to_macro': {}
        }

        # 细微情感信息
        for fine_label, indices in fine_emotion_groups.items():
            if len(indices) == 0:
                continue

            indices_tensor = torch.tensor(indices, device=device, dtype=torch.long)
            emotion_feats = feats[indices_tensor]
            emotion_labels = labels[indices_tensor]
            center = emotion_feats.mean(dim=0)

            emotion_structure['fine_emotions'][fine_label] = {
                'indices': indices_tensor,
                'feats': emotion_feats,
                'labels': emotion_labels,
                'center': center
            }

            # 映射到宏观情感
            if fine_label in ['hn', 'nn', 'wn']:
                emotion_structure['fine_to_macro'][fine_label] = 'negative'
            elif fine_label == 'n':
                emotion_structure['fine_to_macro'][fine_label] = 'neutral'
            else:
                emotion_structure['fine_to_macro'][fine_label] = 'positive'

        # 宏观情感信息
        for macro_label, indices in macro_emotion_groups.items():
            if len(indices) == 0:
                continue

            indices_tensor = torch.tensor(indices, device=device, dtype=torch.long)
            emotion_feats = feats[indices_tensor]
            center = emotion_feats.mean(dim=0)

            emotion_structure['macro_emotions'][macro_label] = {
                'indices': indices_tensor,
                'feats': emotion_feats,
                'center': center
            }

        return emotion_structure

    def _compute_intra_emotion_attraction(self, emotion_structure: dict, I: torch.Tensor) -> torch.Tensor:
        """计算同类细微情感聚合损失：相同细微情感的样本相互靠近"""
        total_loss = emotion_structure['fine_emotions'][list(emotion_structure['fine_emotions'].keys())[0]]['feats'].new_tensor(0.0)
        count = 0
        key_to_idx = self._cluster_index_map()

        for fine_label, emotion_info in emotion_structure['fine_emotions'].items():
            emotion_feats = emotion_info['feats']
            n_samples = emotion_feats.size(0)

            if n_samples < 2:
                continue

            # 获取该细微情感的度量矩阵
            if fine_label in key_to_idx:
                cid = key_to_idx[fine_label]
                M = self._get_metric(cid) + self.beta * I
            else:
                M = I

            # 计算同一细微情感内所有样本对的距离，目标是最小化
            max_pairs = min(self.max_pairs, n_samples * (n_samples - 1) // 2)
            ii, jj = torch.triu_indices(n_samples, n_samples, offset=1, device=emotion_feats.device)

            if ii.numel() > max_pairs:
                sel = torch.randperm(ii.numel(), device=emotion_feats.device)[:max_pairs]
                ii, jj = ii[sel], jj[sel]

            x1, x2 = emotion_feats[ii], emotion_feats[jj]
            distances = _sq_mahalanobis(x1, x2, M)
            total_loss = total_loss + distances.sum()
            count += distances.numel()

        return total_loss / max(count, 1)

    def _compute_fine_emotion_separation(self, emotion_structure: dict) -> torch.Tensor:
        """计算细微情感间分离损失：7种细微情感相互远离"""
        total_loss = emotion_structure['fine_emotions'][list(emotion_structure['fine_emotions'].keys())[0]]['feats'].new_tensor(0.0)
        count = 0
        margin = 2.0  # 细微情感间的最小距离

        fine_emotions = list(emotion_structure['fine_emotions'].keys())

        # 计算所有细微情感对之间的分离损失
        for i in range(len(fine_emotions)):
            for j in range(i + 1, len(fine_emotions)):
                emotion_i = fine_emotions[i]
                emotion_j = fine_emotions[j]

                center_i = emotion_structure['fine_emotions'][emotion_i]['center']
                center_j = emotion_structure['fine_emotions'][emotion_j]['center']

                # 计算中心间距离
                center_distance = torch.norm(center_i - center_j, p=2)

                # 使用margin loss：如果距离小于margin，则产生损失
                separation_loss = torch.clamp(margin - center_distance, min=0.0)
                total_loss = total_loss + separation_loss
                count += 1

                # 额外计算不同细微情感样本间的推远损失
                feats_i = emotion_structure['fine_emotions'][emotion_i]['feats']
                feats_j = emotion_structure['fine_emotions'][emotion_j]['feats']

                # 采样一些样本对来计算推远损失
                n_i, n_j = feats_i.size(0), feats_j.size(0)
                max_cross_pairs = min(self.max_pairs // len(fine_emotions), n_i * n_j)

                if max_cross_pairs > 0:
                    # 随机采样跨情感的样本对
                    sampled_pairs = min(max_cross_pairs, 50)  # 限制计算量
                    for _ in range(sampled_pairs):
                        idx_i = torch.randint(0, n_i, (1,)).item()
                        idx_j = torch.randint(0, n_j, (1,)).item()

                        sample_distance = torch.norm(feats_i[idx_i] - feats_j[idx_j], p=2)
                        cross_loss = torch.clamp(margin - sample_distance, min=0.0)
                        total_loss = total_loss + cross_loss
                        count += 1

        return total_loss / max(count, 1)

    def _compute_macro_emotion_separation(self, emotion_structure: dict) -> torch.Tensor:
        """计算宏观情感类分离损失：积极、中立、消极三大类相互远离"""
        total_loss = emotion_structure['macro_emotions'][list(emotion_structure['macro_emotions'].keys())[0]]['feats'].new_tensor(0.0)
        count = 0
        margin = 5.0  # 宏观情感间的最小距离（比细微情感更大）

        macro_emotions = list(emotion_structure['macro_emotions'].keys())

        # 计算所有宏观情感对之间的分离损失
        for i in range(len(macro_emotions)):
            for j in range(i + 1, len(macro_emotions)):
                emotion_i = macro_emotions[i]
                emotion_j = macro_emotions[j]

                center_i = emotion_structure['macro_emotions'][emotion_i]['center']
                center_j = emotion_structure['macro_emotions'][emotion_j]['center']

                # 计算中心间距离
                center_distance = torch.norm(center_i - center_j, p=2)

                # 使用更大的margin loss：宏观情感应该距离更远
                separation_loss = torch.clamp(margin - center_distance, min=0.0)
                total_loss = total_loss + separation_loss
                count += 1

                # 额外计算不同宏观情感样本间的强推远损失
                feats_i = emotion_structure['macro_emotions'][emotion_i]['feats']
                feats_j = emotion_structure['macro_emotions'][emotion_j]['feats']

                # 采样一些样本对来计算强推远损失
                n_i, n_j = feats_i.size(0), feats_j.size(0)
                max_cross_pairs = min(self.max_pairs // len(macro_emotions), n_i * n_j)

                if max_cross_pairs > 0:
                    # 随机采样跨宏观情感的样本对
                    sampled_pairs = min(max_cross_pairs, 100)  # 宏观分离更重要，采样更多
                    for _ in range(sampled_pairs):
                        idx_i = torch.randint(0, n_i, (1,)).item()
                        idx_j = torch.randint(0, n_j, (1,)).item()

                        sample_distance = torch.norm(feats_i[idx_i] - feats_j[idx_j], p=2)
                        cross_loss = torch.clamp(margin - sample_distance, min=0.0)
                        total_loss = total_loss + cross_loss
                        count += 1

        return total_loss / max(count, 1)
