Model,Acc_2,F1_score,Acc_7,<PERSON><PERSON>,Loss
dmd,"(np.float64(82.93), np.float64(0.0))","(np.float64(82.98), np.float64(0.0))","(np.float64(43.59), np.float64(0.0))","(np.float64(75.37), np.float64(0.0))","(np.float64(75.5), np.float64(0.0))"
dmd,"(np.float64(83.84), np.float64(0.0))","(np.float64(83.89), np.float64(0.0))","(np.float64(46.06), np.float64(0.0))","(np.float64(74.94), np.float64(0.0))","(np.float64(74.88), np.float64(0.0))"
dmd,"(np.float64(83.84), np.float64(0.0))","(np.float64(83.81), np.float64(0.0))","(np.float64(44.46), np.float64(0.0))","(np.float64(79.06), np.float64(0.0))","(np.float64(79.12), np.float64(0.0))"
dmd,"(np.float64(83.84), np.float64(0.0))","(np.float64(83.79), np.float64(0.0))","(np.float64(45.48), np.float64(0.0))","(np.float64(77.1), np.float64(0.0))","(np.float64(77.11), np.float64(0.0))"
dmd,"(np.float64(82.62), np.float64(0.0))","(np.float64(82.61), np.float64(0.0))","(np.float64(44.9), np.float64(0.0))","(np.float64(75.7), np.float64(0.0))","(np.float64(75.75), np.float64(0.0))"
dmd,"(np.float64(83.84), np.float64(0.0))","(np.float64(83.89), np.float64(0.0))","(np.float64(46.06), np.float64(0.0))","(np.float64(74.94), np.float64(0.0))","(np.float64(74.88), np.float64(0.0))"
dmd,"(np.float64(84.3), np.float64(0.0))","(np.float64(84.29), np.float64(0.0))","(np.float64(45.34), np.float64(0.0))","(np.float64(72.76), np.float64(0.0))","(np.float64(72.74), np.float64(0.0))"
dmd,"(np.float64(84.3), np.float64(0.0))","(np.float64(84.29), np.float64(0.0))","(np.float64(45.34), np.float64(0.0))","(np.float64(72.76), np.float64(0.0))","(np.float64(72.74), np.float64(0.0))"
dmd,"(np.float64(84.6), np.float64(0.0))","(np.float64(84.62), np.float64(0.0))","(np.float64(44.75), np.float64(0.0))","(np.float64(72.82), np.float64(0.0))","(np.float64(72.76), np.float64(0.0))"
dmd,"(np.float64(84.15), np.float64(0.0))","(np.float64(84.11), np.float64(0.0))","(np.float64(44.61), np.float64(0.0))","(np.float64(74.14), np.float64(0.0))","(np.float64(74.14), np.float64(0.0))"
dmd,"(np.float64(83.54), np.float64(0.0))","(np.float64(83.57), np.float64(0.0))","(np.float64(44.75), np.float64(0.0))","(np.float64(72.6), np.float64(0.0))","(np.float64(72.56), np.float64(0.0))"
dmd,"(np.float64(51.52), np.float64(0.0))","(np.float64(46.61), np.float64(0.0))","(np.float64(13.99), np.float64(0.0))","(np.float64(142.83), np.float64(0.0))","(np.float64(142.88), np.float64(0.0))"
dmd,"(np.float64(82.01), np.float64(0.0))","(np.float64(82.09), np.float64(0.0))","(np.float64(40.96), np.float64(0.0))","(np.float64(84.63), np.float64(0.0))","(np.float64(84.57), np.float64(0.0))"
dmd,"(np.float64(83.54), np.float64(0.0))","(np.float64(83.54), np.float64(0.0))","(np.float64(44.9), np.float64(0.0))","(np.float64(73.01), np.float64(0.0))","(np.float64(73.09), np.float64(0.0))"
dmd,"(np.float64(81.4), np.float64(0.0))","(np.float64(81.52), np.float64(0.0))","(np.float64(42.27), np.float64(0.0))","(np.float64(82.55), np.float64(0.0))","(np.float64(82.55), np.float64(0.0))"
dmd,"(np.float64(81.71), np.float64(0.0))","(np.float64(81.72), np.float64(0.0))","(np.float64(39.21), np.float64(0.0))","(np.float64(88.28), np.float64(0.0))","(np.float64(88.5), np.float64(0.0))"
dmd,"(np.float64(80.95), np.float64(0.0))","(np.float64(80.94), np.float64(0.0))","(np.float64(39.5), np.float64(0.0))","(np.float64(88.9), np.float64(0.0))","(np.float64(87.74), np.float64(0.0))"
dmd,"(np.float64(42.23), np.float64(0.0))","(np.float64(25.07), np.float64(0.0))","(np.float64(15.31), np.float64(0.0))","(np.float64(151.42), np.float64(0.0))","(np.float64(150.11), np.float64(0.0))"
dmd,"(np.float64(81.4), np.float64(0.0))","(np.float64(81.46), np.float64(0.0))","(np.float64(38.34), np.float64(0.0))","(np.float64(86.99), np.float64(0.0))","(np.float64(87.12), np.float64(0.0))"
dmd,"(np.float64(80.49), np.float64(0.0))","(np.float64(80.58), np.float64(0.0))","(np.float64(36.73), np.float64(0.0))","(np.float64(89.31), np.float64(0.0))","(np.float64(89.32), np.float64(0.0))"
dmd,"(np.float64(51.52), np.float64(0.0))","(np.float64(48.42), np.float64(0.0))","(np.float64(15.6), np.float64(0.0))","(np.float64(144.05), np.float64(0.0))","(np.float64(143.52), np.float64(0.0))"
dmd,"(np.float64(53.51), np.float64(0.0))","(np.float64(51.94), np.float64(0.0))","(np.float64(15.89), np.float64(0.0))","(np.float64(142.5), np.float64(0.0))","(np.float64(141.98), np.float64(0.0))"
dmd,"(np.float64(51.83), np.float64(0.0))","(np.float64(48.4), np.float64(0.0))","(np.float64(14.58), np.float64(0.0))","(np.float64(144.17), np.float64(0.0))","(np.float64(143.69), np.float64(0.0))"
dmd,"(np.float64(75.76), np.float64(0.0))","(np.float64(75.65), np.float64(0.0))","(np.float64(26.82), np.float64(0.0))","(np.float64(111.25), np.float64(0.0))","(np.float64(111.51), np.float64(0.0))"
dmd,"(np.float64(42.07), np.float64(0.0))","(np.float64(25.8), np.float64(0.0))","(np.float64(15.89), np.float64(0.0))","(np.float64(147.41), np.float64(0.0))","(np.float64(147.62), np.float64(0.0))"
dmd,"(np.float64(50.46), np.float64(0.0))","(np.float64(45.38), np.float64(0.0))","(np.float64(15.45), np.float64(0.0))","(np.float64(144.09), np.float64(0.0))","(np.float64(144.02), np.float64(0.0))"
dmd,"(np.float64(84.3), np.float64(0.0))","(np.float64(84.33), np.float64(0.0))","(np.float64(40.67), np.float64(0.0))","(np.float64(75.09), np.float64(0.0))","(np.float64(75.52), np.float64(0.0))"
dmd,"(np.float64(84.15), np.float64(0.0))","(np.float64(84.15), np.float64(0.0))","(np.float64(42.57), np.float64(0.0))","(np.float64(75.52), np.float64(0.0))","(np.float64(74.68), np.float64(0.0))"
dmd,"(np.float64(82.77), np.float64(0.0))","(np.float64(82.88), np.float64(0.0))","(np.float64(39.36), np.float64(0.0))","(np.float64(78.28), np.float64(0.0))","(np.float64(77.85), np.float64(0.0))"
