#!/usr/bin/env python3
"""
可视化数据集中真正的原始特征（完全未经过任何模型处理）
"""

import os
import pdb
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import argparse
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import get_config_regression
from data_loader import MMDataLoader
from utils.functions import setup_seed, assign_gpu
from trains.singleTask.model.dmd import DMD

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_windows_to_linux_path(path):
    """将Windows路径转换为Linux路径格式"""
    if isinstance(path, str):
        # 将反斜杠替换为正斜杠
        path = path.replace('\\', '/')
        # 将Windows驱动器路径转换为WSL路径
        if path.startswith('G:/'):
            path = path.replace('G:/', '/mnt/g/')
        elif path.startswith('C:/'):
            path = path.replace('C:/', '/mnt/c/')
        elif path.startswith('D:/'):
            path = path.replace('D:/', '/mnt/d/')
    return path

def load_data(dataset_name='mosi'):
    """加载数据（不加载模型）"""

    logger.info("Loading configuration...")
    config = get_config_regression('dmd', dataset_name)

    # 添加缺失的配置字段
    config.feature_T = ""  # 空字符串表示使用默认特征
    config.feature_A = ""
    config.feature_V = ""

    # 转换路径为Linux格式
    config.featurePath = convert_windows_to_linux_path(config.featurePath)
    if hasattr(config, 'dataset_root_dir'):
        config.dataset_root_dir = convert_windows_to_linux_path(config.dataset_root_dir)

    logger.info(f"Feature path: {config.featurePath}")

    # 检查数据文件是否存在
    if not os.path.exists(config.featurePath):
        logger.error(f"Data file not found: {config.featurePath}")
        logger.info("Please check if the dataset is properly downloaded and the path is correct.")
        raise FileNotFoundError(f"Data file not found: {config.featurePath}")

    # 设置设备
    config.device = assign_gpu([0])
    logger.info(f"Using device: {config.device}")

    # 设置随机种子
    setup_seed(1111)

    # 加载数据
    logger.info("Loading data...")
    try:
        dataloader = MMDataLoader(config, num_workers=0)
        logger.info("✓ Data loaded successfully")
    except Exception as e:
        logger.error(f"Failed to load data: {str(e)}")
        raise

    return dataloader, config

def load_annotations_from_pkl(config):
    """直接从pkl文件中加载annotations信息"""
    import pickle

    logger.info("Loading annotations from pkl file...")

    with open(config.featurePath, 'rb') as f:
        data = pickle.load(f)

    annotations = {}
    for split in ['train', 'valid', 'test']:
        if split in data:
            # 检查是否有annotations字段
            if 'annotations' in data[split]:
                annotations[split] = data[split]['annotations']
                logger.info(f"Found annotations in {split}: {len(annotations[split])} samples")
            else:
                # 如果没有annotations，从regression_labels转换
                regression_labels = data[split]['regression_labels']
                split_annotations = []
                for label in regression_labels:
                    if label < 0:
                        split_annotations.append('Negative')
                    elif label > 0:
                        split_annotations.append('Positive')
                    else:
                        split_annotations.append('Neutral')
                annotations[split] = split_annotations
                logger.info(f"Converted regression_labels to annotations in {split}: {len(annotations[split])} samples")

    return annotations

def load_trained_model(config, model_path='./pt/dmd.pth'):
    """加载训练好的DMD模型"""
    logger.info("Loading trained DMD model...")

    # 尝试不同的配置来匹配保存的模型
    # 首先尝试当前配置
    try:
        model = DMD(config)
        model.to(config.device)
        model.load_state_dict(torch.load(model_path, map_location=config.device))
        logger.info(f"✓ Loaded model weights from {model_path} with current config")
        model.eval()
        return model
    except Exception as e:
        logger.warning(f"Failed to load with current config: {str(e)}")

    # 如果失败，尝试aligned配置
    try:
        logger.info("Trying with aligned data configuration...")
        # 创建aligned配置
        aligned_config = config.copy()
        aligned_config.need_data_aligned = True

        model = DMD(aligned_config)
        model.to(config.device)
        model.load_state_dict(torch.load(model_path, map_location=config.device))
        logger.info(f"✓ Loaded model weights from {model_path} with aligned config")
        model.eval()
        return model
    except Exception as e:
        logger.warning(f"Failed to load with aligned config: {str(e)}")

    # 如果还是失败，尝试strict=False
    try:
        logger.info("Trying with strict=False...")
        model = DMD(config)
        model.to(config.device)
        model.load_state_dict(torch.load(model_path, map_location=config.device), strict=False)
        logger.warning(f"⚠ Loaded model weights from {model_path} with strict=False (some weights may be missing)")
        model.eval()
        return model
    except Exception as e:
        logger.error(f"Failed to load model: {str(e)}")
        logger.warning(f"⚠ Using randomly initialized model.")
        model = DMD(config)
        model.to(config.device)
        model.eval()
        return model

def extract_encoder_features(model, dataloader, config, num_batches=3, encoder_type='common', fixed_batches=None):
    """提取模型的公共编码器或私有编码器特征

    Args:
        encoder_type: 'common' for 公共编码器特征, 'private' for 私有编码器特征
        fixed_batches: 可选，传入固定的batch列表以避免每次遍历DataLoader触发重新shuffle
    """

    encoder_features = {
        'text': [],
        'audio': [],
        'video': []
    }
    labels_list = []
    indices_list = []

    logger.info(f"Extracting {encoder_type} encoder features...")

    batch_count = 0
    if fixed_batches is not None:
        iterator = fixed_batches
        total_batches = len(fixed_batches)
    else:
        iterator = dataloader['test']
        total_batches = min(num_batches, len(dataloader['test']))

    with torch.no_grad():  # 不计算梯度
        for batch_data in iterator:
            if fixed_batches is None and batch_count >= num_batches:
                break

            # 获取输入数据
            text = batch_data['text'].to(config.device)
            audio = batch_data['audio'].to(config.device)
            video = batch_data['vision'].to(config.device)
            labels = batch_data['labels']['M']
            indices = batch_data['index']

            # 前向传播获取模型输出
            try:
                model_output = model(text, audio, video)

                if encoder_type == 'common':
                    # 提取公共编码器特征
                    c_l = model_output['c_l']  # [seq_len, batch, dim]
                    c_v = model_output['c_v']  # [seq_len, batch, dim]
                    c_a = model_output['c_a']  # [seq_len, batch, dim]

                    # 转换为 [batch, seq_len, dim] 并取平均
                    text_features = c_l.permute(1, 0, 2).mean(dim=1)  # [batch, dim]
                    video_features = c_v.permute(1, 0, 2).mean(dim=1)  # [batch, dim]
                    audio_features = c_a.permute(1, 0, 2).mean(dim=1)  # [batch, dim]

                    logger.info(f"Common encoder features - Text: {text_features.shape}, Video: {video_features.shape}, Audio: {audio_features.shape}")

                elif encoder_type == 'private':
                    # 提取私有编码器特征
                    s_l = model_output['s_l']  # [seq_len, batch, dim]
                    s_v = model_output['s_v']  # [seq_len, batch, dim]
                    s_a = model_output['s_a']  # [seq_len, batch, dim]

                    # 转换为 [batch, seq_len, dim] 并取平均
                    text_features = s_l.permute(1, 0, 2).mean(dim=1)  # [batch, dim]
                    video_features = s_v.permute(1, 0, 2).mean(dim=1)  # [batch, dim]
                    audio_features = s_a.permute(1, 0, 2).mean(dim=1)  # [batch, dim]

                    logger.info(f"Private encoder features - Text: {text_features.shape}, Video: {video_features.shape}, Audio: {audio_features.shape}")

                else:
                    raise ValueError(f"Unknown encoder_type: {encoder_type}")

            except Exception as e:
                logger.warning(f"Error in model forward pass: {str(e)}")
                # 如果模型前向传播失败，使用输入特征的平均值作为后备
                text_features = text.mean(dim=1)  # [batch, text_dim]
                audio_features = audio.mean(dim=1)  # [batch, audio_dim]
                video_features = video.mean(dim=1)   # [batch, video_dim]
                logger.info(f"Using fallback features")

            encoder_features['text'].append(text_features.cpu())
            encoder_features['audio'].append(audio_features.cpu())
            encoder_features['video'].append(video_features.cpu())
            labels_list.append(labels)
            indices_list.append(indices)

            batch_count += 1
            logger.info(f"Processed batch {batch_count}/{total_batches}")

    # 连接所有批次
    for modality in encoder_features:
        encoder_features[modality] = torch.cat(encoder_features[modality], dim=0)

    labels_all = torch.cat(labels_list, dim=0)
    indices_all = torch.cat(indices_list, dim=0)

    logger.info(f"{encoder_type.capitalize()} encoder feature extraction completed!")
    logger.info(f"Text features shape: {encoder_features['text'].shape}")
    logger.info(f"Audio features shape: {encoder_features['audio'].shape}")
    logger.info(f"Video features shape: {encoder_features['video'].shape}")
    logger.info(f"Labels shape: {labels_all.shape}")
    logger.info(f"Indices shape: {indices_all.shape}")

    return encoder_features, labels_all, indices_all

def extract_model_fusion_features(model, dataloader, config, num_batches=3, fixed_batches=None):
    """提取模型的最终融合特征"""

    fusion_features = []
    labels_list = []
    indices_list = []

    logger.info("Extracting model fusion features...")

    batch_count = 0
    if fixed_batches is not None:
        iterator = fixed_batches
        total_batches = len(fixed_batches)
    else:
        iterator = dataloader['test']
        total_batches = min(num_batches, len(dataloader['test']))

    with torch.no_grad():  # 不计算梯度
        for batch_data in iterator:
            if fixed_batches is None and batch_count >= num_batches:
                break

            # 获取输入数据
            text = batch_data['text'].to(config.device)
            audio = batch_data['audio'].to(config.device)
            video = batch_data['vision'].to(config.device)
            labels = batch_data['labels']['M']
            indices = batch_data['index']

            # 前向传播获取模型输出
            # try:
            model_output = model(text, audio, video)
            fusion_feature = model_output['fusion_feature']

                # 提取最终融合特征
                # 尝试获取不同层次的特征
            #     if 'repr_l_hetero' in model_output and 'repr_v_hetero' in model_output and 'repr_a_hetero' in model_output:
            #         # 使用交叉注意力特征
            #         last_h_l = model_output['repr_l_hetero']  # [batch, dim]
            #         last_h_v = model_output['repr_v_hetero']  # [batch, dim]
            #         last_h_a = model_output['repr_a_hetero']  # [batch, dim]
            #         fusion_feature = torch.cat([last_h_l, last_h_v, last_h_a], dim=1)
            #         logger.info(f"Using hetero features: {fusion_feature.shape}")
            #     elif 'repr_l_homo' in model_output and 'repr_v_homo' in model_output and 'repr_a_homo' in model_output:
            #         # 使用同质特征
            #         repr_l = model_output['repr_l_homo']  # [batch, dim]
            #         repr_v = model_output['repr_v_homo']  # [batch, dim]
            #         repr_a = model_output['repr_a_homo']  # [batch, dim]
            #         fusion_feature = torch.cat([repr_l, repr_v, repr_a], dim=1)
            #         logger.info(f"Using homo features: {fusion_feature.shape}")
            #     else:
            #         # 使用原始特征作为后备
            #         origin_l = model_output.get('origin_l', text.mean(dim=1))  # [batch, dim]
            #         origin_v = model_output.get('origin_v', video.mean(dim=1))  # [batch, dim]
            #         origin_a = model_output.get('origin_a', audio.mean(dim=1))  # [batch, dim]
            #         fusion_feature = torch.cat([origin_l, origin_v, origin_a], dim=1)
            #         logger.info(f"Using origin features: {fusion_feature.shape}")
            #
            # except Exception as e:
            #     logger.warning(f"Error in model forward pass: {str(e)}")
            #     # 如果模型前向传播失败，使用输入特征的平均值作为后备
            #     fusion_feature = torch.cat([
            #         text.mean(dim=1),  # [batch, text_dim]
            #         audio.mean(dim=1),  # [batch, audio_dim]
            #         video.mean(dim=1)   # [batch, video_dim]
            #     ], dim=1)
            #     logger.info(f"Using fallback features: {fusion_feature.shape}")

            fusion_features.append(fusion_feature.cpu())
            labels_list.append(labels)
            indices_list.append(indices)

            batch_count += 1
            logger.info(f"Processed batch {batch_count}/{total_batches}")

    # 连接所有批次
    fusion_features_all = torch.cat(fusion_features, dim=0)
    labels_all = torch.cat(labels_list, dim=0)
    indices_all = torch.cat(indices_list, dim=0)

    logger.info("Model fusion feature extraction completed!")
    logger.info(f"Fusion features shape: {fusion_features_all.shape}")
    logger.info(f"Labels shape: {labels_all.shape}")
    logger.info(f"Indices shape: {indices_all.shape}")

    return fusion_features_all, labels_all, indices_all

def extract_raw_features(dataloader, config, num_batches=3, fixed_batches=None):
    """提取数据集中的原始特征（完全未经过模型处理）"""

    raw_features = {
        'text_raw': [],
        'audio_raw': [],
        'video_raw': []
    }
    labels_list = []
    indices_list = []  # 记录样本索引以便后续匹配annotations

    logger.info("Extracting RAW dataset features (no model processing)...")

    batch_count = 0
    if fixed_batches is not None:
        iterator = fixed_batches
        total_batches = len(fixed_batches)
    else:
        iterator = dataloader['test']
        total_batches = min(num_batches, len(dataloader['test']))

    for batch_data in iterator:
        if fixed_batches is None and batch_count >= num_batches:
            break

        # 直接获取数据加载器中的原始特征
        # 这些是数据集中的原始特征，没有经过任何模型处理
        text_raw = batch_data['text']      # 原始文本特征 (BERT或GloVe特征)
        audio_raw = batch_data['audio']    # 原始音频特征
        video_raw = batch_data['vision']   # 原始视频特征
        labels = batch_data['labels']['M']
        indices = batch_data['index']      # 获取样本索引

        # 存储原始特征
        raw_features['text_raw'].append(text_raw)
        raw_features['audio_raw'].append(audio_raw)
        raw_features['video_raw'].append(video_raw)
        labels_list.append(labels)
        indices_list.append(indices)

        batch_count += 1
        logger.info(f"Processed batch {batch_count}/{total_batches}")

    # 连接所有批次
    for modality in raw_features:
        raw_features[modality] = torch.cat(raw_features[modality], dim=0)

    labels_all = torch.cat(labels_list, dim=0)
    indices_all = torch.cat(indices_list, dim=0)

    logger.info("Raw feature extraction completed!")
    logger.info(f"Text features shape: {raw_features['text_raw'].shape}")
    logger.info(f"Audio features shape: {raw_features['audio_raw'].shape}")
    logger.info(f"Video features shape: {raw_features['video_raw'].shape}")
    logger.info(f"Labels shape: {labels_all.shape}")
    logger.info(f"Indices shape: {indices_all.shape}")

    return raw_features, labels_all, indices_all

def prepare_features_for_tsne(raw_features, max_samples=500):
    """准备特征用于t-SNE可视化"""

    logger.info("Preparing RAW features for t-SNE...")

    # 获取特征
    text_features = raw_features['text_raw'].numpy()  # [N, seq_len, feature_dim]
    audio_features = raw_features['audio_raw'].numpy()  # [N, seq_len, feature_dim]
    video_features = raw_features['video_raw'].numpy()  # [N, seq_len, feature_dim]

    # 限制样本数量以加快t-SNE计算
    n_samples = min(max_samples, text_features.shape[0])
    text_features = text_features[:n_samples]
    audio_features = audio_features[:n_samples]
    video_features = video_features[:n_samples]

    logger.info(f"Using {n_samples} samples for t-SNE")

    # 将特征展平为2D：[N, seq_len * feature_dim]
    text_flat = text_features.reshape(n_samples, -1)
    audio_flat = audio_features.reshape(n_samples, -1)
    video_flat = video_features.reshape(n_samples, -1)

    logger.info(f"Flattened shapes - Text: {text_flat.shape}, Audio: {audio_flat.shape}, Video: {video_flat.shape}")

    # 使用PCA将所有特征降维到相同维度
    from sklearn.decomposition import PCA

    # 选择一个合适的目标维度（不能超过样本数量）
    max_components = min(n_samples - 1, 30)  # 最多30维，且不能超过样本数-1
    target_dim = min(text_flat.shape[1], audio_flat.shape[1], video_flat.shape[1], max_components)
    logger.info(f"Target dimension for PCA: {target_dim} (max allowed: {max_components})")

    # 对每种模态分别进行PCA降维
    pca_text = PCA(n_components=target_dim)
    pca_audio = PCA(n_components=target_dim)
    pca_video = PCA(n_components=target_dim)

    text_pca = pca_text.fit_transform(text_flat)
    audio_pca = pca_audio.fit_transform(audio_flat)
    video_pca = pca_video.fit_transform(video_flat)

    logger.info(f"PCA shapes - Text: {text_pca.shape}, Audio: {audio_pca.shape}, Video: {video_pca.shape}")
    logger.info(f"PCA explained variance - Text: {pca_text.explained_variance_ratio_.sum():.3f}, "
                f"Audio: {pca_audio.explained_variance_ratio_.sum():.3f}, "
                f"Video: {pca_video.explained_variance_ratio_.sum():.3f}")

    # 标准化PCA后的特征
    scaler_text = StandardScaler()
    scaler_audio = StandardScaler()
    scaler_video = StandardScaler()

    text_normalized = scaler_text.fit_transform(text_pca)
    audio_normalized = scaler_audio.fit_transform(audio_pca)
    video_normalized = scaler_video.fit_transform(video_pca)

    logger.info("Features normalized after PCA")

    return text_normalized, audio_normalized, video_normalized, n_samples

def apply_tsne_and_visualize_with_sentiment(text_features, audio_features, video_features,
                                          sentiment_labels, n_samples, perplexity=20, n_iter=500,
                                          save_path='raw_dataset_features_sentiment_tsne.png'):
    """应用t-SNE并根据样本分组可视化三种模态的原始特征，每个样本三模态同色，不同样本不同色"""

    logger.info("Applying t-SNE with sample-based coloring...")

    # 合并所有特征
    all_features = np.vstack([text_features, audio_features, video_features])

    # 创建模态标签（0: text, 1: audio, 2: video）
    modality_labels = np.array([0] * n_samples + [1] * n_samples + [2] * n_samples)

    # 复制情感标签以匹配所有模态
    all_sentiment_labels = np.tile(sentiment_labels, 3)

    # 应用t-SNE
    tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=n_iter, random_state=42, verbose=1)
    features_2d = tsne.fit_transform(all_features)

    logger.info("t-SNE completed!")

    # 按样本分配颜色，支持样本数大于colormap最大色数时自动循环
    import matplotlib
    # tab20最多20种色，样本数大于20时自动循环
    cmap = matplotlib.cm.get_cmap('tab20')
    sample_colors = [matplotlib.colors.rgb2hex(cmap(i % cmap.N)) for i in range(n_samples)]
    # 如果样本数大于colormap最大色数，警告用户
    if n_samples > cmap.N:
        logger.warning(f"样本数 {n_samples} 超过 tab20 色板最大色数 {cmap.N}，部分样本颜色会重复。建议自定义色板或减少batch样本数。")

    modality_markers = {0: 'o', 1: 's', 2: '^'}
    modality_names = {0: 'Text', 1: 'Audio', 2: 'Video'}

    plt.figure(figsize=(15, 10))

    # 每个样本三模态同色，不同样本不同色
    for i in range(n_samples):
        for modality in [0, 1, 2]:
            idx = modality * n_samples + i
            plt.scatter(features_2d[idx, 0], features_2d[idx, 1],
                        c=sample_colors[i],
                        marker=modality_markers[modality],
                        alpha=0.8, s=70,
                        edgecolors='black', linewidth=0.7,
                        label=f'Sample {i+1}' if modality == 0 else None)

    plt.title('t-SNE Visualization: Each Sample Modalities Share Color, Different Samples Different Colors',
              fontsize=14, fontweight='bold')
    plt.xlabel('t-SNE Component 1', fontsize=12)
    plt.ylabel('t-SNE Component 2', fontsize=12)

    # 只显示每个样本一次的图例
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    plt.legend(by_label.values(), by_label.keys(), bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9, title='Sample Index')
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    stats_text = f'Samples: {n_samples}\nPerplexity: {perplexity}, Iterations: {n_iter}'
    plt.figtext(0.02, 0.02, stats_text, fontsize=9, ha='left', va='bottom')

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    logger.info(f"Sample-based encoder features visualization saved to {save_path}")
    try:
        plt.show()
    except:
        logger.info("Cannot display plot in current environment")

    return features_2d, modality_labels, all_sentiment_labels

def prepare_encoder_features_for_tsne(encoder_features, max_samples=500):
    """准备编码器特征用于t-SNE可视化"""

    logger.info("Preparing encoder features for t-SNE...")

    # 获取特征
    text_features = encoder_features['text'].numpy()  # [N, feature_dim]
    audio_features = encoder_features['audio'].numpy()  # [N, feature_dim]
    video_features = encoder_features['video'].numpy()  # [N, feature_dim]

    # 限制样本数量以加快t-SNE计算
    n_samples = min(max_samples, text_features.shape[0])
    text_features = text_features[:n_samples]
    audio_features = audio_features[:n_samples]
    video_features = video_features[:n_samples]

    logger.info(f"Using {n_samples} samples for t-SNE")
    logger.info(f"Encoder features shapes - Text: {text_features.shape}, Audio: {audio_features.shape}, Video: {video_features.shape}")

    # 标准化特征
    scaler_text = StandardScaler()
    scaler_audio = StandardScaler()
    scaler_video = StandardScaler()

    text_normalized = scaler_text.fit_transform(text_features)
    audio_normalized = scaler_audio.fit_transform(audio_features)
    video_normalized = scaler_video.fit_transform(video_features)

    logger.info("Encoder features normalized")

    return text_normalized, audio_normalized, video_normalized, n_samples

def prepare_fusion_features_for_tsne(fusion_features, max_samples=500):
    """准备融合特征用于t-SNE可视化"""

    logger.info("Preparing fusion features for t-SNE...")

    # 转换为numpy数组
    fusion_features_np = fusion_features.numpy()  # [N, feature_dim]

    # 限制样本数量以加快t-SNE计算
    n_samples = min(max_samples, fusion_features_np.shape[0])
    fusion_features_np = fusion_features_np[:n_samples]

    logger.info(f"Using {n_samples} samples for t-SNE")
    logger.info(f"Fusion features shape: {fusion_features_np.shape}")

    # 标准化特征
    scaler = StandardScaler()
    fusion_features_normalized = scaler.fit_transform(fusion_features_np)

    logger.info("Fusion features normalized")

    return fusion_features_normalized, n_samples

def apply_tsne_and_visualize_fusion_features(fusion_features, sentiment_labels, n_samples,
                                           perplexity=20, n_iter=500,
                                           save_path='model_fusion_features_sentiment_tsne.png'):
    """应用t-SNE并根据情感标签可视化模型融合特征"""

    logger.info("Applying t-SNE to fusion features...")

    logger.info(f"Fusion features shape: {fusion_features.shape}")
    logger.info(f"Sentiment labels shape: {sentiment_labels.shape}")

    # 应用t-SNE
    tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=n_iter, random_state=42, verbose=1)
    features_2d = tsne.fit_transform(fusion_features)

    logger.info("t-SNE completed!")

    # 定义颜色
    sentiment_colors = {
        'Positive': '#FF6B6B',    # 红色
        'Neutral': '#555555',     # 深灰色
        'Negative': '#45B7D1'     # 蓝色
    }

    # 可视化
    plt.figure(figsize=(12, 8))

    # 为每种情感绘制点
    for sentiment in ['Positive', 'Neutral', 'Negative']:
        # 找到对应的点
        mask = (sentiment_labels == sentiment)
        if np.any(mask):
            points_2d = features_2d[mask]
            plt.scatter(points_2d[:, 0], points_2d[:, 1],
                       c=sentiment_colors[sentiment],
                       alpha=0.7, s=60,
                       label=f'{sentiment}',
                       edgecolors='black', linewidth=0.5)

    plt.title('t-SNE Visualization of Model Fusion Features by Sentiment\n' +
              '(Colors: Red=Positive, Gray=Neutral, Blue=Negative)',
              fontsize=14, fontweight='bold')
    plt.xlabel('t-SNE Component 1', fontsize=12)
    plt.ylabel('t-SNE Component 2', fontsize=12)

    # 创建图例
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    sentiment_counts = {}
    for sentiment in ['Positive', 'Neutral', 'Negative']:
        count = np.sum(sentiment_labels == sentiment)
        sentiment_counts[sentiment] = count

    stats_text = f'Model Fusion Features - Samples: {n_samples}\n'
    stats_text += f'Sentiment distribution:\n'
    stats_text += f'  Positive: {sentiment_counts["Positive"]}\n'
    stats_text += f'  Neutral: {sentiment_counts["Neutral"]}\n'
    stats_text += f'  Negative: {sentiment_counts["Negative"]}\n'
    stats_text += f'Perplexity: {perplexity}, Iterations: {n_iter}'

    plt.figtext(0.02, 0.02, stats_text, fontsize=9, ha='left', va='bottom')

    plt.tight_layout()

    # 保存图像
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    logger.info(f"Fusion features visualization saved to {save_path}")

    # 显示图像（如果在支持的环境中）
    try:
        plt.show()
    except:
        logger.info("Cannot display plot in current environment")

    return features_2d

def apply_tsne_and_visualize_encoder_features(text_features, audio_features, video_features,
                                            sentiment_labels, n_samples, encoder_type='common',
                                            perplexity=20, n_iter=500,
                                            save_path='encoder_features_sentiment_tsne.png'):
    """应用t-SNE并根据模态和情感标签可视化编码器特征"""

    logger.info(f"Applying t-SNE to {encoder_type} encoder features...")

    # 合并所有特征
    all_features = np.vstack([text_features, audio_features, video_features])

    # 创建模态标签（0: text, 1: audio, 2: video）
    modality_labels = np.array([0] * n_samples + [1] * n_samples + [2] * n_samples)

    # 复制情感标签以匹配所有模态
    all_sentiment_labels = np.tile(sentiment_labels, 3)

    logger.info(f"Combined features shape: {all_features.shape}")
    logger.info(f"Modality labels shape: {modality_labels.shape}")
    logger.info(f"Sentiment labels shape: {all_sentiment_labels.shape}")

    # 应用t-SNE
    tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=n_iter, random_state=42, verbose=1)
    features_2d = tsne.fit_transform(all_features)

    logger.info("t-SNE completed!")

    # 定义颜色和形状
    sentiment_colors = {
        'Positive': '#FF6B6B',    # 红色
        'Neutral': '#555555',     # 深灰色
        'Negative': '#45B7D1'     # 蓝色
    }

    modality_markers = {
        0: 'o',  # 圆形 - Text
        1: 's',  # 方形 - Audio
        2: '^'   # 三角形 - Video
    }

    modality_names = {0: 'Text', 1: 'Audio', 2: 'Video'}

    # 可视化
    plt.figure(figsize=(15, 10))

    # 为每种模态和情感组合绘制点
    for modality in [0, 1, 2]:
        for sentiment in ['Positive', 'Neutral', 'Negative']:
            # 找到对应的点
            mask = (modality_labels == modality) & (all_sentiment_labels == sentiment)
            if np.any(mask):
                points_2d = features_2d[mask]
                plt.scatter(points_2d[:, 0], points_2d[:, 1],
                           c=sentiment_colors[sentiment],
                           marker=modality_markers[modality],
                           alpha=0.7, s=60,
                           label=f'{modality_names[modality]} - {sentiment}',
                           edgecolors='black', linewidth=0.5)

    encoder_type_title = encoder_type.capitalize()
    plt.title(f't-SNE Visualization of {encoder_type_title} Encoder Features by Modality and Sentiment\n' +
              '(Shapes: ○Text, ■Audio, ▲Video; Colors: Red=Positive, Gray=Neutral, Blue=Negative)',
              fontsize=14, fontweight='bold')
    plt.xlabel('t-SNE Component 1', fontsize=12)
    plt.ylabel('t-SNE Component 2', fontsize=12)

    # 创建图例
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    sentiment_counts = {}
    for sentiment in ['Positive', 'Neutral', 'Negative']:
        count = np.sum(sentiment_labels == sentiment)
        sentiment_counts[sentiment] = count

    stats_text = f'{encoder_type_title} Encoder Features - Samples per modality: {n_samples}\n'
    stats_text += f'Sentiment distribution:\n'
    stats_text += f'  Positive: {sentiment_counts["Positive"]}\n'
    stats_text += f'  Neutral: {sentiment_counts["Neutral"]}\n'
    stats_text += f'  Negative: {sentiment_counts["Negative"]}\n'
    stats_text += f'Perplexity: {perplexity}, Iterations: {n_iter}'

    plt.figtext(0.02, 0.02, stats_text, fontsize=9, ha='left', va='bottom')

    plt.tight_layout()

    # 保存图像
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    logger.info(f"{encoder_type_title} encoder features visualization saved to {save_path}")

    # 显示图像（如果在支持的环境中）
    try:
        plt.show()
    except:
        logger.info("Cannot display plot in current environment")

    return features_2d, modality_labels, all_sentiment_labels

def main():
    parser = argparse.ArgumentParser(description='Visualize features using t-SNE with sentiment labels')
    parser.add_argument('--dataset_name', type=str, default='mosi', help='Dataset name')
    parser.add_argument('--num_batches', type=int, default=3, help='Number of batches to process')
    parser.add_argument('--max_samples', type=int, default=5000, help='Maximum samples for t-SNE')
    parser.add_argument('--perplexity', type=int, default=5, help='t-SNE perplexity')
    parser.add_argument('--n_iter', type=int, default=1000, help='t-SNE iterations')
    parser.add_argument('--visualization_type', type=str, default='raw',
                        choices=['raw', 'fusion', 'common', 'private', 'all'],
                        help='Type of visualization: raw (raw features), fusion (model fusion features), '
                             'common (common encoder features), private (private encoder features), '
                             'all (all types)')
    parser.add_argument('--model_path', type=str, default='./pt/dmd.pth', help='Path to trained model')
    parser.add_argument('--save_path_raw', type=str, default='raw_dataset_features_sentiment_tsne.png',
                        help='Save path for raw features visualization')
    parser.add_argument('--save_path_fusion', type=str, default='model_fusion_features_sentiment_tsne.png',
                        help='Save path for fusion features visualization')
    parser.add_argument('--save_path_common', type=str, default='common_encoder_features_sentiment_tsne.png',
                        help='Save path for common encoder features visualization')
    parser.add_argument('--save_path_private', type=str, default='private_encoder_features_sentiment_tsne.png',
                        help='Save path for private encoder features visualization')

    args = parser.parse_args()

    # 预定义，避免在不同可视化分支中引用前未赋值
    fixed_batches = None

    try:
        # 加载数据
        dataloader, config = load_data(args.dataset_name)

        # 加载annotations信息
        annotations = load_annotations_from_pkl(config)

        # 固定前 num_batches 个 test 批次，确保 raw/common/private/fusion 使用相同样本集合
        fixed_batches = []
        for batch_data in dataloader['test']:
            fixed_batches.append(batch_data)
            if len(fixed_batches) >= args.num_batches:
                break
        if len(fixed_batches) == 0:
            logger.warning("No batches collected from test dataloader for fixed_batches.")

        # pdb.set_trace()
        if args.visualization_type in ['raw', 'all']:
            logger.info("=" * 60)
            logger.info("VISUALIZING RAW DATASET FEATURES")
            logger.info("=" * 60)

            # 提取原始特征（使用固定的相同batch）
            raw_features, labels, indices = extract_raw_features(dataloader, config, args.num_batches, fixed_batches=fixed_batches)

            # 获取对应的情感标签
            test_annotations = annotations['test']
            sentiment_labels = []
            for idx in indices.numpy():
                sentiment_labels.append(test_annotations[idx])
            sentiment_labels = np.array(sentiment_labels)

            logger.info(f"Sentiment distribution:")
            unique, counts = np.unique(sentiment_labels, return_counts=True)
            for sentiment, count in zip(unique, counts):
                logger.info(f"  {sentiment}: {count}")

            # 准备t-SNE特征
            text_features, audio_features, video_features, n_samples = prepare_features_for_tsne(
                raw_features, args.max_samples)

            # 限制情感标签数量以匹配特征
            sentiment_labels_raw = sentiment_labels[:n_samples]

            # 应用t-SNE并可视化（每个样本三模态同色、样本间异色）
            features_2d, modality_labels, all_sentiment_labels = apply_tsne_and_visualize_with_sentiment(
                text_features, audio_features, video_features, sentiment_labels_raw, n_samples,
                args.perplexity, args.n_iter, args.save_path_raw)

            logger.info("✓ Raw dataset feature visualization completed!")
            logger.info(f"✓ Output saved to: {args.save_path_raw}")

        # 需要模型的可视化类型
        if args.visualization_type in ['fusion', 'common', 'private', 'all']:
            # 加载训练好的模型
            model = load_trained_model(config, args.model_path)

        if args.visualization_type in ['fusion', 'all']:
            logger.info("=" * 60)
            logger.info("VISUALIZING MODEL FUSION FEATURES")
            logger.info("=" * 60)

            # 提取模型融合特征（使用固定的相同batch）
            fusion_features, labels, indices = extract_model_fusion_features(model, dataloader, config, args.num_batches, fixed_batches=fixed_batches)

            # 获取对应的情感标签
            test_annotations = annotations['test']
            sentiment_labels = []
            for idx in indices.numpy():
                sentiment_labels.append(test_annotations[idx])
            sentiment_labels = np.array(sentiment_labels)

            # 准备t-SNE特征
            fusion_features_normalized, n_samples = prepare_fusion_features_for_tsne(
                fusion_features, args.max_samples)

            # 限制情感标签数量以匹配特征
            sentiment_labels_fusion = sentiment_labels[:n_samples]

            # 应用t-SNE并可视化融合特征
            features_2d_fusion = apply_tsne_and_visualize_fusion_features(
                fusion_features_normalized, sentiment_labels_fusion, n_samples,
                args.perplexity, args.n_iter, args.save_path_fusion)

            logger.info("✓ Model fusion feature visualization completed!")
            logger.info(f"✓ Output saved to: {args.save_path_fusion}")


        if args.visualization_type in ['common', 'all']:
            logger.info("=" * 60)
            logger.info("VISUALIZING COMMON ENCODER FEATURES")
            logger.info("=" * 60)

            # 提取公共编码器特征（使用固定的相同batch）
            common_features, labels, indices = extract_encoder_features(
                model, dataloader, config, args.num_batches, encoder_type='common', fixed_batches=fixed_batches)

            # 获取对应的情感标签
            test_annotations = annotations['test']
            sentiment_labels = []
            for idx in indices.numpy():
                sentiment_labels.append(test_annotations[idx])
            sentiment_labels = np.array(sentiment_labels)

            # 准备t-SNE特征
            text_features, audio_features, video_features, n_samples = prepare_encoder_features_for_tsne(
                common_features, args.max_samples)

            # 限制情感标签数量以匹配特征
            sentiment_labels_common = sentiment_labels[:n_samples]

            # 应用t-SNE并可视化公共编码器特征
            features_2d_common, modality_labels, all_sentiment_labels = apply_tsne_and_visualize_encoder_features(
                text_features, audio_features, video_features, sentiment_labels_common, n_samples,
                encoder_type='common', perplexity=args.perplexity, n_iter=args.n_iter,
                save_path=args.save_path_common)

            logger.info("✓ Common encoder feature visualization completed!")
            logger.info(f"✓ Output saved to: {args.save_path_common}")

        if args.visualization_type in ['private', 'all']:
            logger.info("=" * 60)
            logger.info("VISUALIZING PRIVATE ENCODER FEATURES")
            logger.info("=" * 60)

            # 提取私有编码器特征（使用固定的相同batch）
            private_features, labels, indices = extract_encoder_features(
                model, dataloader, config, args.num_batches, encoder_type='private', fixed_batches=fixed_batches)

            # 获取对应的情感标签
            test_annotations = annotations['test']
            sentiment_labels = []
            for idx in indices.numpy():
                sentiment_labels.append(test_annotations[idx])
            sentiment_labels = np.array(sentiment_labels)

            # 准备t-SNE特征
            text_features, audio_features, video_features, n_samples = prepare_encoder_features_for_tsne(
                private_features, args.max_samples)

            # 限制情感标签数量以匹配特征
            sentiment_labels_private = sentiment_labels[:n_samples]

            # 应用t-SNE并可视化私有编码器特征
            features_2d_private, modality_labels, all_sentiment_labels = apply_tsne_and_visualize_encoder_features(
                text_features, audio_features, video_features, sentiment_labels_private, n_samples,
                encoder_type='private', perplexity=args.perplexity, n_iter=args.n_iter,
                save_path=args.save_path_private)

            logger.info("✓ Private encoder feature visualization completed!")
            logger.info(f"✓ Output saved to: {args.save_path_private}")

        logger.info("=" * 60)
        logger.info("✓ Feature visualization completed successfully!")
        if args.visualization_type == 'raw':
            logger.info("✓ Raw features: Markers denote modalities; colors distinguish samples (each sample's three modalities share one color).")
            logger.info("✓ Shapes: ○Text, ■Audio, ▲Video")
        elif args.visualization_type == 'fusion':
            logger.info("✓ Fusion features: Colors represent sentiment")
        elif args.visualization_type == 'common':
            logger.info("✓ Common encoder features: Shapes represent modalities, colors represent sentiment")
            logger.info("✓ Shapes: ○Text, ■Audio, ▲Video")
        elif args.visualization_type == 'private':
            logger.info("✓ Private encoder features: Shapes represent modalities, colors represent sentiment")
            logger.info("✓ Shapes: ○Text, ■Audio, ▲Video")
        else:  # all
            logger.info("✓ Raw features: Shapes represent modalities, colors represent sentiment")
            logger.info("✓ Fusion features: Colors represent sentiment")
            logger.info("✓ Common encoder features: Shapes represent modalities, colors represent sentiment")
            logger.info("✓ Private encoder features: Shapes represent modalities, colors represent sentiment")
            logger.info("✓ Shapes: ○Text, ■Audio, ▲Video")
        logger.info("✓ Colors: Red=Positive, Gray=Neutral, Blue=Negative")
        logger.info("=" * 60)

    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise

if __name__ == "__main__":
    main()
