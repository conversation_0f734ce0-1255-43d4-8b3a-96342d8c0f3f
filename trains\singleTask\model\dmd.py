"""
here is the mian backbone for DMD containing feature decoupling and multimodal transformers
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from ...subNets import BertTextEncoder
from ...subNets.transformers_encoder.transformer import TransformerEncoder
import pdb

class DMD(nn.Module):
    def __init__(self, args):
        super(DMD, self).__init__()
        if args.use_bert:
            self.text_model = BertTextEncoder(use_finetune=args.use_finetune, transformers=args.transformers,
                                              pretrained=args.pretrained)
        self.use_bert = args.use_bert
        dst_feature_dims, nheads = args.dst_feature_dim_nheads
        if args.dataset_name == 'mosi':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else:
                self.len_l, self.len_v, self.len_a = 50, 500, 375
        if args.dataset_name == 'mosei':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else:
                self.len_l, self.len_v, self.len_a = 50, 500, 500
        self.orig_d_l, self.orig_d_a, self.orig_d_v = args.feature_dims #768, 5, 20
        self.d_l = self.d_a = self.d_v = dst_feature_dims #50
        self.num_heads = nheads
        self.layers = args.nlevels
        self.attn_dropout = args.attn_dropout
        self.attn_dropout_a = args.attn_dropout_a
        self.attn_dropout_v = args.attn_dropout_v
        self.relu_dropout = args.relu_dropout
        self.embed_dropout = args.embed_dropout
        self.res_dropout = args.res_dropout
        self.output_dropout = args.output_dropout
        self.text_dropout = args.text_dropout
        self.attn_mask = args.attn_mask
        output_dim = 1

        # 1. Temporal convolutional layers for initial feature
        self.proj_l = nn.Conv1d(self.orig_d_l, self.d_l, kernel_size=args.conv1d_kernel_size_l, padding=0, bias=False)
        self.proj_a = nn.Conv1d(self.orig_d_a, self.d_a, kernel_size=args.conv1d_kernel_size_a, padding=0, bias=False)
        self.proj_v = nn.Conv1d(self.orig_d_v, self.d_v, kernel_size=args.conv1d_kernel_size_v, padding=0, bias=False)

        # 2. Modality-invariant encoder (公共编码器)
        self.encoder_c = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)

        # for align c_l, c_v, c_a
        self.align_c_l = nn.Linear(self.d_l * (self.len_l - args.conv1d_kernel_size_l + 1), self.d_l)
        self.align_c_v = nn.Linear(self.d_v * (self.len_v - args.conv1d_kernel_size_v + 1), self.d_v)
        self.align_c_a = nn.Linear(self.d_a * (self.len_a - args.conv1d_kernel_size_a + 1), self.d_a)

        self.self_attentions_c_l = self.get_network(self_type='l')
        self.self_attentions_c_v = self.get_network(self_type='v')
        self.self_attentions_c_a = self.get_network(self_type='a')

        # 3. 公共编码器的最终输出层
        self.proj1_c = nn.Linear(self.d_l * 3, self.d_l * 3)
        self.proj2_c = nn.Linear(self.d_l * 3, self.d_l * 3)
        self.out_layer_c = nn.Linear(self.d_l * 3, output_dim)

    def get_network(self, self_type='l', layers=-1):
        if self_type in ['l', 'al', 'vl']:
            embed_dim, attn_dropout = self.d_l, self.attn_dropout
        elif self_type in ['a', 'la', 'va']:
            embed_dim, attn_dropout = self.d_a, self.attn_dropout_a
        elif self_type in ['v', 'lv', 'av']:
            embed_dim, attn_dropout = self.d_v, self.attn_dropout_v
        elif self_type == 'l_mem':
            embed_dim, attn_dropout = 2 * self.d_l, self.attn_dropout
        elif self_type == 'a_mem':
            embed_dim, attn_dropout = 2 * self.d_a, self.attn_dropout
        elif self_type == 'v_mem':
            embed_dim, attn_dropout = 2 * self.d_v, self.attn_dropout
        else:
            raise ValueError("Unknown network type")

        return TransformerEncoder(embed_dim=embed_dim,
                                  num_heads=self.num_heads,
                                  layers=max(self.layers, layers),
                                  attn_dropout=attn_dropout,
                                  relu_dropout=self.relu_dropout,
                                  res_dropout=self.res_dropout,
                                  embed_dropout=self.embed_dropout,
                                  attn_mask=self.attn_mask)

    def forward(self, text, audio, video, is_distill=False):
        # 文本编码
        if self.use_bert:
            text = self.text_model(text) #[16, 3, 50]->[16, 50, 768]

        # 输入预处理
        x_l = F.dropout(text.transpose(1, 2), p=self.text_dropout, training=self.training) #[16, 768, 50]
        x_a = audio.transpose(1, 2) #[16, 5, 50]
        x_v = video.transpose(1, 2) #[16, 20, 50]

        # 特征投影
        proj_x_l = x_l if self.orig_d_l == self.d_l else self.proj_l(x_l) #[16, 50, 46]
        proj_x_a = x_a if self.orig_d_a == self.d_a else self.proj_a(x_a) #[16, 50, 46]
        proj_x_v = x_v if self.orig_d_v == self.d_v else self.proj_v(x_v) #[16, 50, 46]

        # 公共编码器 - 提取模态不变特征
        c_l = self.encoder_c(proj_x_l) #[16, 50, 46]
        c_v = self.encoder_c(proj_x_v) #[16, 50, 46]
        c_a = self.encoder_c(proj_x_a) #[16, 50, 46]

        # 特征对齐
        c_l_sim = self.align_c_l(c_l.contiguous().view(x_l.size(0), -1)) # [16, 50, 46]->[16, 50*46] = [16, 2300]->[16, 50]
        c_v_sim = self.align_c_v(c_v.contiguous().view(x_l.size(0), -1)) # [16, 50, 46]->[16, 50*46] = [16, 2300]->[16, 50]
        c_a_sim = self.align_c_a(c_a.contiguous().view(x_l.size(0), -1)) # [16, 50, 46]->[16, 50*46] = [16, 2300]->[16, 50]

        # 转换维度用于自注意力
        c_l = c_l.permute(2, 0, 1) #[46, 16, 50]
        c_v = c_v.permute(2, 0, 1) #[46, 16, 50]
        c_a = c_a.permute(2, 0, 1) #[46, 16, 50]

        # 公共编码器的自注意力机制
        c_l_att = self.self_attentions_c_l(c_l) #[46, 16, 50]
        if type(c_l_att) == tuple:
            c_l_att = c_l_att[0]
        c_l_att = c_l_att[-1] #[16, 50]

        c_v_att = self.self_attentions_c_v(c_v)
        if type(c_v_att) == tuple:
            c_v_att = c_v_att[0]
        c_v_att = c_v_att[-1]

        c_a_att = self.self_attentions_c_a(c_a)
        if type(c_a_att) == tuple:
            c_a_att = c_a_att[0]
        c_a_att = c_a_att[-1]

        # 多模态融合
        c_fusion = torch.cat([c_l_att, c_v_att, c_a_att], dim=1) #[16, 50+50+50]->[16, 150]

        # 最终预测层
        c_proj = self.proj2_c(
            F.dropout(F.relu(self.proj1_c(c_fusion), inplace=True), p=self.output_dropout,
                      training=self.training)) #[16, 150]
        c_proj += c_fusion #残差链接
        output = self.out_layer_c(c_proj) #[16, 150]->[16, 1]



        res = {
            'c_l': c_l,
            'c_v': c_v,
            'c_a': c_a,
            'c_l_sim': c_l_sim,
            'c_v_sim': c_v_sim,
            'c_a_sim': c_a_sim,
            'output_logit': output,
            'fusion_feature': c_fusion
        }
        return res