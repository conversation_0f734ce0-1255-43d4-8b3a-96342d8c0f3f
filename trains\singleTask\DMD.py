import logging
import pdb

import numpy as np
import torch
import torch.nn as nn
from torch import optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
from tqdm import tqdm
from ..utils import MetricsTop, dict_to_str
from .HingeLoss import Hinge<PERSON>oss
from .MMLCNLoss import MetricCluster<PERSON>ingeLoss

logger = logging.getLogger('MMSA')

class MSE(nn.Module):
    def __init__(self):
        super(MSE, self).__init__()

    def forward(self, pred, real):
        diffs = torch.add(real, -pred)
        n = torch.numel(diffs.data)
        mse = torch.sum(diffs.pow(2)) / n
        return mse

class DMD():
    def __init__(self, args):
        self.args = args
        self.criterion = nn.L1Loss()
        self.cosine = nn.CosineEmbeddingLoss()
        self.metrics = MetricsTop(args.train_mode).getMetics(args.dataset_name)
        self.MSE = MSE()
        self.sim_loss = HingeLoss()
        # Metric-Cluster Hinge Loss (formula-10) for shared encoder features
        beta = getattr(args, 'mcl_beta', 1e-3)
        v1 = getattr(args, 'mcl_v1', 1.0)
        v2 = getattr(args, 'mcl_v2', 1.0)
        alpha = getattr(args, 'mcl_alpha', 0.3)
        kmeans_iters = getattr(args, 'mcl_kmeans_iters', 10)
        max_pairs = getattr(args, 'mcl_max_pairs', 256)
        shared_feat_dim = getattr(args, 'shared_feat_dim', 50)
        self.metric_cluster_loss = MetricClusterHingeLoss(
            feature_dim=shared_feat_dim, beta=beta, v1=v1, v2=v2, alpha=alpha,
            kmeans_iters=kmeans_iters, max_pairs_per_cluster=max_pairs
        ).to(self.args.device)

    def do_train(self, model, dataloader, return_epoch_results=False):

        # 只有DMD模型，移除图蒸馏网络
        if isinstance(model, list):
            # 如果传入的是列表，只使用第一个模型（DMD模型）
            model = model[0]

        params = list(model.parameters()) + list(self.metric_cluster_loss.parameters())

        optimizer = optim.Adam(params, lr=self.args.learning_rate)
        scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, verbose=True, patience=self.args.patience)

        epochs, best_epoch = 0, 0
        if return_epoch_results:
            epoch_results = {
                'train': [],
                'valid': [],
                'test': []
            }
        min_or_max = 'min' if self.args.KeyEval in ['Loss'] else 'max'
        best_valid = 1e8 if min_or_max == 'min' else 0

        while True:
            epochs += 1
            y_pred, y_true = [], []
            model.train()

            train_loss = 0.0
            left_epochs = self.args.update_epochs
            with tqdm(dataloader['train']) as td:
                for batch_data in td:

                    if left_epochs == self.args.update_epochs:
                        optimizer.zero_grad()
                    left_epochs -= 1
                    vision = batch_data['vision'].to(self.args.device)
                    audio = batch_data['audio'].to(self.args.device)
                    text = batch_data['text'].to(self.args.device)
                    labels = batch_data['labels']['M'].to(self.args.device)
                    labels = labels.view(-1, 1)

                    output = model(text, audio, vision, is_distill=True)



                    # task loss - 只使用主要输出
                    loss_task = self.criterion(output['output_logit'], labels)

                    # metric-cluster hinge loss (formula-10) over shared encoder features
                    # Use the common encoder features prepared for similarity head
                    c_l, c_v, c_a = output['c_l_sim'], output['c_v_sim'], output['c_a_sim']
                    feats_list = []
                    labels_list = []
                    for i in range(labels.size(0)):
                        # stack three modalities per sample
                        feats_list.append(c_l[i].view(1, -1))
                        feats_list.append(c_v[i].view(1, -1))
                        feats_list.append(c_a[i].view(1, -1))
                        # replicate the scalar sentiment label for each modality feature
                        labels_list.extend([labels[i].view(1)])
                        labels_list.extend([labels[i].view(1)])
                        labels_list.extend([labels[i].view(1)])
                    feats_mc = torch.cat(feats_list, dim=0)  # [3B, F]
                    labels_mc = torch.cat(labels_list, dim=0).view(-1)  # [3B]
                    loss_mcl = self.metric_cluster_loss(feats_mc, labels_mc)

                    # 简化的总损失：只包含任务损失和度量聚类损失
                    combined_loss = loss_task + loss_mcl * 0.1

                    combined_loss.backward()

                    # pdb.set_trace()

                    if self.args.grad_clip != -1.0:
                        params = list(model.parameters()) + list(self.metric_cluster_loss.parameters())
                        nn.utils.clip_grad_value_(params, self.args.grad_clip)

                    train_loss += combined_loss.item()

                    y_pred.append(output['output_logit'].cpu())
                    y_true.append(labels.cpu())
                    if not left_epochs:
                        optimizer.step()
                        left_epochs = self.args.update_epochs
                if not left_epochs:
                    # update
                    optimizer.step()

            train_loss = train_loss / len(dataloader['train'])
            pred, true = torch.cat(y_pred), torch.cat(y_true)
            train_results = self.metrics(pred, true)
            logger.info(
                f">> Epoch: {epochs} "
                f"TRAIN-({self.args.model_name}) [{epochs - best_epoch}/{epochs}/{self.args.cur_seed}] "
                f">> total_loss: {round(train_loss, 4)} "
                f"{dict_to_str(train_results)}"
            )
            # validation
            val_results = self.do_test(model, dataloader['valid'], mode="VAL")
            test_results = self.do_test(model, dataloader['test'], mode="TEST")
            cur_valid = val_results[self.args.KeyEval]
            scheduler.step(val_results['Loss'])
            # save each epoch model
            torch.save(model.state_dict(), './pt/' + str(epochs) + '.pth')
            # save best model
            isBetter = cur_valid <= (best_valid - 1e-6) if min_or_max == 'min' else cur_valid >= (best_valid + 1e-6)
            if isBetter:
                best_valid, best_epoch = cur_valid, epochs
                # save model
                model_save_path = './pt/dmd.pth'
                torch.save(model.state_dict(), model_save_path)

            if return_epoch_results:
                train_results["Loss"] = train_loss
                epoch_results['train'].append(train_results)
                epoch_results['valid'].append(val_results)
                test_results = self.do_test(model, dataloader['test'], mode="TEST")
                epoch_results['test'].append(test_results)
            # early stop
            if epochs - best_epoch >= self.args.early_stop:
                return epoch_results if return_epoch_results else None

    def do_test(self, model, dataloader, mode="VAL", return_sample_results=False):

        model.eval()
        y_pred, y_true = [], []

        eval_loss = 0.0
        if return_sample_results:
            ids, sample_results = [], []
            all_labels = []
            features = {
                "Feature_t": [],
                "Feature_a": [],
                "Feature_v": [],
                "Feature_f": [],
            }

        with torch.no_grad():
            with tqdm(dataloader) as td:
                for batch_data in td:
                    vision = batch_data['vision'].to(self.args.device)
                    audio = batch_data['audio'].to(self.args.device)
                    text = batch_data['text'].to(self.args.device)
                    labels = batch_data['labels']['M'].to(self.args.device)
                    labels = labels.view(-1, 1)
                    output = model(text, audio, vision, is_distill=True)
                    loss = self.criterion(output['output_logit'], labels)
                    eval_loss += loss.item()
                    y_pred.append(output['output_logit'].cpu())
                    y_true.append(labels.cpu())

        eval_loss = eval_loss / len(dataloader)
        pred, true = torch.cat(y_pred), torch.cat(y_true)

        eval_results = self.metrics(pred, true)
        eval_results["Loss"] = round(eval_loss, 4)
        logger.info(f"{mode}-({self.args.model_name}) >> {dict_to_str(eval_results)}")

        if return_sample_results:
            eval_results["Ids"] = ids
            eval_results["SResults"] = sample_results
            for k in features.keys():
                features[k] = np.concatenate(features[k], axis=0)
            eval_results['Features'] = features
            eval_results['Labels'] = all_labels

        return eval_results