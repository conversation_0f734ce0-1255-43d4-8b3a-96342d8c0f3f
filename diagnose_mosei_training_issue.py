#!/usr/bin/env python3
"""
MOSEI训练停滞问题诊断和解决方案

问题现象：训练到70%进度时停滞并被kill
可能原因：内存不足、GPU内存泄漏、数据加载问题等
"""

import os
import sys
import psutil
import torch
import gc
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_system_resources():
    """检查系统资源状态"""
    logger.info("=== 系统资源检查 ===")
    
    # CPU和内存信息
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    swap = psutil.swap_memory()
    
    logger.info(f"CPU使用率: {cpu_percent}%")
    logger.info(f"内存总量: {memory.total / (1024**3):.2f} GB")
    logger.info(f"内存已用: {memory.used / (1024**3):.2f} GB ({memory.percent}%)")
    logger.info(f"内存可用: {memory.available / (1024**3):.2f} GB")
    logger.info(f"交换分区: {swap.used / (1024**3):.2f} GB / {swap.total / (1024**3):.2f} GB")
    
    # GPU信息
    if torch.cuda.is_available():
        logger.info(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            gpu_memory = torch.cuda.get_device_properties(i).total_memory
            gpu_allocated = torch.cuda.memory_allocated(i)
            gpu_cached = torch.cuda.memory_reserved(i)
            logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            logger.info(f"  总显存: {gpu_memory / (1024**3):.2f} GB")
            logger.info(f"  已分配: {gpu_allocated / (1024**3):.2f} GB")
            logger.info(f"  已缓存: {gpu_cached / (1024**3):.2f} GB")
    else:
        logger.warning("未检测到CUDA设备")

def analyze_mosei_data_size():
    """分析MOSEI数据集大小"""
    logger.info("=== MOSEI数据集分析 ===")
    
    # MOSEI vs MOSI 对比
    datasets_info = {
        'MOSI': {
            'train_samples': 1284,
            'feature_dims': [768, 5, 20],
            'batch_size': 16
        },
        'MOSEI': {
            'train_samples': 16326,  # 约12.7倍于MOSI
            'feature_dims': [768, 74, 35],  # 音频和视频特征维度更大
            'batch_size': 16
        }
    }
    
    for dataset, info in datasets_info.items():
        logger.info(f"{dataset}数据集:")
        logger.info(f"  训练样本数: {info['train_samples']}")
        logger.info(f"  特征维度: {info['feature_dims']}")
        logger.info(f"  批次大小: {info['batch_size']}")
        
        # 估算单个batch的内存使用
        text_dim, audio_dim, video_dim = info['feature_dims']
        seq_len = 50  # 序列长度
        batch_size = info['batch_size']
        
        # 估算每个模态的内存使用 (float32 = 4 bytes)
        text_memory = batch_size * seq_len * text_dim * 4 / (1024**2)  # MB
        audio_memory = batch_size * seq_len * audio_dim * 4 / (1024**2)  # MB
        video_memory = batch_size * seq_len * video_dim * 4 / (1024**2)  # MB
        total_batch_memory = text_memory + audio_memory + video_memory
        
        logger.info(f"  单batch内存估算:")
        logger.info(f"    文本: {text_memory:.2f} MB")
        logger.info(f"    音频: {audio_memory:.2f} MB")
        logger.info(f"    视频: {video_memory:.2f} MB")
        logger.info(f"    总计: {total_batch_memory:.2f} MB")

def identify_potential_issues():
    """识别潜在问题"""
    logger.info("=== 潜在问题分析 ===")
    
    issues = [
        {
            'issue': '内存不足',
            'description': 'MOSEI数据集比MOSI大12.7倍，音频特征维度从5增加到74，视频特征从20增加到35',
            'symptoms': ['训练到70%时停滞', '进程被系统kill', 'OOM错误'],
            'solutions': [
                '减小batch_size从16到8或4',
                '使用梯度累积',
                '启用混合精度训练',
                '增加系统内存或使用更大显存的GPU'
            ]
        },
        {
            'issue': 'GPU内存泄漏',
            'description': '模型包含多个子网络，可能存在内存泄漏',
            'symptoms': ['GPU内存持续增长', '训练后期变慢'],
            'solutions': [
                '在每个epoch后清理GPU缓存',
                '使用torch.cuda.empty_cache()',
                '检查是否有未释放的张量引用'
            ]
        },
        {
            'issue': '数据加载器问题',
            'description': 'num_workers设置可能导致内存问题',
            'symptoms': ['数据加载变慢', '内存使用异常'],
            'solutions': [
                '减少num_workers数量',
                '使用pin_memory=False',
                '检查数据预处理是否有内存泄漏'
            ]
        },
        {
            'issue': '模型复杂度过高',
            'description': 'DMD模型包含多个编码器和蒸馏网络',
            'symptoms': ['显存不足', '训练速度慢'],
            'solutions': [
                '减小模型隐藏层维度',
                '使用模型并行',
                '启用梯度检查点'
            ]
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        logger.info(f"{i}. {issue['issue']}")
        logger.info(f"   描述: {issue['description']}")
        logger.info(f"   症状: {', '.join(issue['symptoms'])}")
        logger.info(f"   解决方案:")
        for solution in issue['solutions']:
            logger.info(f"     - {solution}")
        logger.info("")

def generate_optimized_config():
    """生成优化的配置"""
    logger.info("=== 优化配置建议 ===")
    
    optimized_configs = {
        'low_memory': {
            'batch_size': 4,
            'num_workers': 0,
            'pin_memory': False,
            'gradient_accumulation_steps': 4,
            'mixed_precision': True,
            'description': '低内存配置 - 适用于显存不足的情况'
        },
        'balanced': {
            'batch_size': 8,
            'num_workers': 2,
            'pin_memory': True,
            'gradient_accumulation_steps': 2,
            'mixed_precision': True,
            'description': '平衡配置 - 在性能和内存之间平衡'
        },
        'high_performance': {
            'batch_size': 16,
            'num_workers': 4,
            'pin_memory': True,
            'gradient_accumulation_steps': 1,
            'mixed_precision': False,
            'description': '高性能配置 - 适用于大显存GPU'
        }
    }
    
    for config_name, config in optimized_configs.items():
        logger.info(f"{config_name.upper()}配置:")
        logger.info(f"  描述: {config['description']}")
        for key, value in config.items():
            if key != 'description':
                logger.info(f"  {key}: {value}")
        logger.info("")

def create_memory_monitoring_script():
    """创建内存监控脚本"""
    script_content = '''
import torch
import psutil
import time
import logging

class MemoryMonitor:
    def __init__(self, log_interval=10):
        self.log_interval = log_interval
        self.logger = logging.getLogger('MemoryMonitor')
        
    def log_memory_usage(self, step_name=""):
        # 系统内存
        memory = psutil.virtual_memory()
        
        # GPU内存
        if torch.cuda.is_available():
            gpu_allocated = torch.cuda.memory_allocated() / (1024**3)
            gpu_cached = torch.cuda.memory_reserved() / (1024**3)
            
            self.logger.info(f"{step_name} - RAM: {memory.percent}% "
                           f"GPU Allocated: {gpu_allocated:.2f}GB "
                           f"GPU Cached: {gpu_cached:.2f}GB")
        else:
            self.logger.info(f"{step_name} - RAM: {memory.percent}%")
    
    def cleanup_gpu_memory(self):
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()

# 使用示例：
# monitor = MemoryMonitor()
# monitor.log_memory_usage("训练开始")
# # ... 训练代码 ...
# monitor.cleanup_gpu_memory()
'''
    
    with open('memory_monitor.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    logger.info("已创建内存监控脚本: memory_monitor.py")

def main():
    logger.info("🔍 MOSEI训练停滞问题诊断工具")
    logger.info("=" * 50)
    
    # 检查系统资源
    check_system_resources()
    print()
    
    # 分析数据集大小
    analyze_mosei_data_size()
    print()
    
    # 识别潜在问题
    identify_potential_issues()
    
    # 生成优化配置
    generate_optimized_config()
    
    # 创建监控脚本
    create_memory_monitoring_script()
    
    logger.info("🔧 建议的解决步骤:")
    logger.info("1. 首先尝试减小batch_size到8或4")
    logger.info("2. 在训练脚本中添加内存监控")
    logger.info("3. 启用混合精度训练")
    logger.info("4. 定期清理GPU缓存")
    logger.info("5. 如果问题持续，考虑使用梯度累积")

if __name__ == "__main__":
    main()
